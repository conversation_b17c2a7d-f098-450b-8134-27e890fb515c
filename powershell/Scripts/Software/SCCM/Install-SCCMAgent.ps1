<#
.SYNOPSIS
    Installs the SCCM (System Center Configuration Manager) client on Windows systems.

.DESCRIPTION
    This script installs the SCCM client on Windows systems with basic configuration.
    Uses standardized config file for default settings.

.PARAMETER ManagementPoint
    The primary SCCM management point FQDN or IP address.

.PARAMETER SiteCode
    The SCCM site code (typically 3 characters).

.PARAMETER ServerName
    Optional remote server name. If provided, the script will install the SCCM client on the remote server instead of locally.

.PARAMETER ClientSourcePath
    The path to the SCCM client installer (ccmsetup.exe). Optional - uses config default if not provided.

.PARAMETER Force
    Force reinstallation even if client is already installed.

.PARAMETER OutputJson
    Return results in JSON format.

.PARAMETER UseStoredCredentials
    Use stored credentials from XML credential files for service accounts.

.PARAMETER CredentialTarget
    The target environment to determine which credential file to use. Valid values: 'ppe', 'dev', 'admin', 'cli'. If not specified, defaults to production domain credentials.

.EXAMPLE
    .\Install-SCCMAgent.ps1 -ManagementPoint "sccm01.domain.com" -SiteCode "ABC"

.EXAMPLE
    .\Install-SCCMAgent.ps1 -ManagementPoint "sccm01.domain.com" -SiteCode "ABC" -Force -OutputJson

.EXAMPLE
    .\Install-SCCMAgent.ps1 -ManagementPoint "sccm01.domain.com" -SiteCode "ABC" -UseStoredCredentials -CredentialTarget "admin"

.EXAMPLE
    # Remote installation - automatically uses stored credentials
    .\Install-SCCMAgent.ps1 -ManagementPoint "sccm01.domain.com" -SiteCode "ABC" -ServerName "RemoteServer01" -Force

.EXAMPLE
    # Remote installation with specific credential target
    .\Install-SCCMAgent.ps1 -ManagementPoint "sccm01.domain.com" -SiteCode "ABC" -ServerName "RemoteServer01" -CredentialTarget "admin"

.EXAMPLE
    # Remote installation using ConfigSCCM function with stored credentials
    # Automatically uses stored credentials from XML file based on script parameters
    ConfigSCCM -serverName "RemoteServer01" -ManagementPoint "sccm01.domain.com" -SiteCode "ABC" -Force

.EXAMPLE
    # Remote installation with specific credential target for API usage
    ConfigSCCM -serverName "RemoteServer01" -ManagementPoint "sccm01.domain.com" -SiteCode "ABC" -CredentialTarget "admin" -Force

.NOTES
    File Name   : Install-SCCMAgent.ps1
    Author      : Rudi van Zyl
    Requires    : PowerShell 5.0 or later, Administrator privileges
    Requires    : scriptConfigs.json file in same directory
    Compatible  : Windows Server 2012, 2016, 2019, 2022
    
    Version 1.0 - Initial script for SCCM client installation
    Version 1.1 - Updated to use standardized config file and credential system
    Version 1.2 - Simplified script with essential parameters only
    Version 1.3 - Added remote installation capabilities and API-friendly JSON returns
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [string]$ManagementPoint,
    
    [Parameter(Mandatory = $true)]
    [string]$SiteCode,
    
    [string]$ServerName,
    
    [string]$ClientSourcePath,
    
    [switch]$Force,
    
    [switch]$OutputJson,
    
    [switch]$UseStoredCredentials,
    
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidUsingPlainTextForPassword', 'CredentialTarget', Justification = 'CredentialTarget is not a password - it specifies which credential file to use')]
    [string]$CredentialTarget
)

#Requires -Version 5
#Requires -RunAsAdministrator

### Global Variables ###
$currentPath = split-path -parent $MyInvocation.MyCommand.Definition
$mainConfigPath = Join-Path (Split-Path (Split-Path (Split-Path $currentPath -Parent) -Parent) -Parent) "Configs\scriptConfigs.json"
$config = Get-Content -Path $mainConfigPath | ConvertFrom-Json

### Script Variables ###
$ErrorActionPreference = "Stop"
$tempPath = "C:\Temp\ServerInstalls\SCCM"
$LogPath = "C:\Windows\Temp\SCCMClient_Install.log"

### SCCM Configuration Settings - Loaded from scriptConfigs.json ###
$defaultSourcePath = $config.SCCMConfig.SCCMClientPath

### SCCM Installation Variables ###
$SMSMP = $ManagementPoint
$SMSSITECODE = $SiteCode
$CCMINSTALLDIR = "C:\Windows\CCM"
$SMSCACHEDIR = "C:\Windows\ccmcache"
$SMSCACHESIZE = 5120
$CCMHTTPPORT = 80
$CCMHTTPSPORT = 443
$CCMHTTPSSTATE = 31
$CCMFIRSTCERT = 1

### Functions ###
function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Success,
        
        [Parameter(Mandatory = $true)]
        [string]$Status,
        
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [hashtable]$Data = @{}
    )
   
    $clientInfo = @{}
    try {
        $ccmExec = Get-Service -Name "CcmExec" -ErrorAction SilentlyContinue
        if ($ccmExec) {
            $clientInfo.ServiceStatus = $ccmExec.Status.ToString()
        }
        
        $ccmSetupVersion = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\SMS\Setup" -Name "Full Version" -ErrorAction SilentlyContinue
        if ($ccmSetupVersion) {
            $clientInfo.ClientVersion = $ccmSetupVersion."Full Version"
        }
        
        $siteAssignment = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\SMS\Mobile Client" -Name "AssignedSiteCode" -ErrorAction SilentlyContinue
        if ($siteAssignment) {
            $clientInfo.AssignedSiteCode = $siteAssignment.AssignedSiteCode
        }
        
        $mgmtPoint = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\SMS\Mobile Client" -Name "GPRequestedMPs" -ErrorAction SilentlyContinue
        if ($mgmtPoint) {
            $clientInfo.ManagementPoints = $mgmtPoint.GPRequestedMPs
        }
    }
    catch {
        # Ignore errors during data collection
    }
    
    $objectReturn = @{
        computerName      = $env:COMPUTERNAME
        SMSMP             = $SMSMP
        SMSSITECODE       = $SMSSITECODE
        CCMINSTALLDIR     = $CCMINSTALLDIR
        SMSCACHEDIR       = $SMSCACHEDIR
        SMSCACHESIZE      = $SMSCACHESIZE
        CCMHTTPPORT       = $CCMHTTPPORT
        CCMHTTPSPORT      = $CCMHTTPSPORT
        CCMHTTPSSTATE     = $CCMHTTPSSTATE
        clientInfo        = $clientInfo
        timeStamp         = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }
    
    if ($Data.Count -gt 0) {
        foreach ($key in $Data.Keys) {
            $objectReturn[$key] = $Data[$key]
        }
    }
    
    $jsonResponse = @{
        success = $Success
        status  = $Status
        message = $Message
        data    = $objectReturn
    }
    
    return ($jsonResponse | ConvertTo-Json -Depth 4)
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    Write-Host $logMessage
    Add-Content -Path $LogPath -Value $logMessage -ErrorAction SilentlyContinue
}

function Test-SCCMClientInstalled {
    try {
        $ccmExec = Get-Service -Name "CcmExec" -ErrorAction SilentlyContinue
        $ccmSetup = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\SMS\Setup" -ErrorAction SilentlyContinue
        
        if ($ccmExec -and $ccmSetup) {
            Write-Log "SCCM client is already installed. Version: $($ccmSetup.'Full Version')"
            return $true
        }
        return $false
    }
    catch {
        Write-Log "Error checking SCCM client installation: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Get-SCCMClientInstaller {
    try {
        if ($ClientSourcePath -and (Test-Path $ClientSourcePath)) {
            Write-Log "Using provided SCCM client installer: $ClientSourcePath"
            return $ClientSourcePath
        }
        
        # Try the default source path from config
        if ($defaultSourcePath -and (Test-Path $defaultSourcePath)) {
            Write-Log "Using default SCCM client installer from config: $defaultSourcePath"
            return $defaultSourcePath
        }
        
        $commonPaths = @(
            "C:\Windows\ccmsetup\ccmsetup.exe",
            "C:\Temp\ccmsetup.exe",
            "$tempPath\ccmsetup.exe"
        )
        
        foreach ($path in $commonPaths) {
            if (Test-Path $path) {
                Write-Log "Found SCCM client installer at: $path"
                return $path
            }
        }
        
        throw "SCCM client installer not found. Please provide ClientSourcePath parameter or place installer in common paths."
    }
    catch {
        Write-Log "Error getting SCCM client installer: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Install-SCCMClient {
    param(
        [string]$InstallerPath
    )
    
    try {
        Write-Log "Starting SCCM client installation..."
        
        $arguments = @()
        $arguments += "SMSMP=$SMSMP"
        $arguments += "SMSSITECODE=$SMSSITECODE"
        $arguments += "SMSCACHESIZE=$SMSCACHESIZE"
        $arguments += "SMSCACHEDIR=`"$SMSCACHEDIR`""
        $arguments += "CCMHTTPPORT=$CCMHTTPPORT"
        $arguments += "CCMHTTPSPORT=$CCMHTTPSPORT"
        $arguments += "CCMHTTPSSTATE=$CCMHTTPSSTATE"
        $arguments += "CCMFIRSTCERT=$CCMFIRSTCERT"
        $arguments += "CCMINSTALLDIR=`"$CCMINSTALLDIR`""
        
        $argumentString = $arguments -join " "
        Write-Log "Installation command: `"$InstallerPath`" $argumentString"
        
        $installProcess = Start-Process -FilePath $InstallerPath -ArgumentList $argumentString -Wait -PassThru -NoNewWindow
        
        if ($installProcess.ExitCode -eq 0) {
            Write-Log "SCCM client installation completed successfully"
            
            Write-Log "Waiting for SCCM services to start..."
            $timeout = 300 # 5 minutes
            $timer = 0
            
            do {
                Start-Sleep -Seconds 10
                $timer += 10
                $ccmExec = Get-Service -Name "CcmExec" -ErrorAction SilentlyContinue
                
                if ($ccmExec -and $ccmExec.Status -eq "Running") {
                    Write-Log "CcmExec service is running"
                    break
                }
                
                if ($timer -ge $timeout) {
                    Write-Log "Timeout waiting for CcmExec service to start" "WARNING"
                    break
                }
            } while ($true)
            
            return $true
        }
        else {
            Write-Log "SCCM client installation failed with exit code: $($installProcess.ExitCode)" "ERROR"
            return $false
        }
    }
    catch {
        Write-Log "Error during SCCM client installation: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Test-SCCMClientConfiguration {
    try {
        Write-Log "Validating SCCM client configuration..."
        
        $assignedSite = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\SMS\Mobile Client" -Name "AssignedSiteCode" -ErrorAction SilentlyContinue
        if ($assignedSite -and $assignedSite.AssignedSiteCode -eq $SMSSITECODE) {
            Write-Log "Client is correctly assigned to site code: $SMSSITECODE"
        }
        else {
            Write-Log "Client site assignment issue. Expected: $SMSSITECODE, Actual: $($assignedSite.AssignedSiteCode)" "WARNING"
        }
        
        $mgmtPoints = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\SMS\Mobile Client" -Name "GPRequestedMPs" -ErrorAction SilentlyContinue
        if ($mgmtPoints) {
            Write-Log "Management points configured: $($mgmtPoints.GPRequestedMPs)"
        }
        
        try {
            $clientCert = Get-ChildItem -Path "Cert:\LocalMachine\SMS" -ErrorAction SilentlyContinue | Where-Object { $_.Subject -like "*SMS*" }
            if ($clientCert) {
                Write-Log "SCCM client certificate found and valid"
            }
            else {
                Write-Log "SCCM client certificate not found" "WARNING"
            }
        }
        catch {
            Write-Log "Could not check client certificate status" "WARNING"
        }
        
        return $true
    }
    catch {
        Write-Log "Error validating client configuration: $($_.Exception.Message)" "WARNING"
        return $false
    }
}

function Uninstall-SCCMClient {
    try {
        Write-Log "Uninstalling existing SCCM client..."
        
        $ccmSetupPath = "C:\Windows\ccmsetup\ccmsetup.exe"
        if (Test-Path $ccmSetupPath) {
            $uninstallProcess = Start-Process -FilePath $ccmSetupPath -ArgumentList "/uninstall" -Wait -PassThru -NoNewWindow
            if ($uninstallProcess.ExitCode -eq 0) {
                Write-Log "SCCM client uninstalled successfully using ccmsetup"
            }
        }
        else {
            Write-Log "ccmsetup.exe not found, trying alternative uninstall method..."
            $ccmClient = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Configuration Manager Client*" }
            if ($ccmClient) {
                $ccmClient.Uninstall() | Out-Null
                Write-Log "SCCM client uninstalled using WMI"
            }
        }
        
        Start-Sleep -Seconds 30
        
        $ccmExec = Get-Service -Name "CcmExec" -ErrorAction SilentlyContinue
        if (-not $ccmExec) {
            Write-Log "SCCM client uninstallation verified"
            return $true
        }
        else {
            Write-Log "SCCM client may not have been completely uninstalled" "WARNING"
            return $false
        }
    }
    catch {
        Write-Log "Error during SCCM client uninstallation: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function Show-CCMSetupVariables {
    Write-Log "=== CCMSetup Variables ==="
    Write-Log "SMSMP: $SMSMP"
    Write-Log "SMSSITECODE: $SMSSITECODE"
    Write-Log "CCMINSTALLDIR: $CCMINSTALLDIR"
    Write-Log "SMSCACHEDIR: $SMSCACHEDIR"
    Write-Log "SMSCACHESIZE: $SMSCACHESIZE"
    Write-Log "CCMHTTPPORT: $CCMHTTPPORT"
    Write-Log "CCMHTTPSPORT: $CCMHTTPSPORT"
    Write-Log "CCMHTTPSSTATE: $CCMHTTPSSTATE"
    Write-Log "CCMFIRSTCERT: $CCMFIRSTCERT"
    Write-Log "=========================="
}

function Get-SCCMCredentials {
    Write-Log "Getting SCCM service credentials..."
    
    if ($UseStoredCredentials) {
        try {
            $credPath = $config.GlobalConfig.CredPath
            $credFile = "$credPath\prdDomainCreds.xml"
            
            if ($CredentialTarget) {
                switch ($CredentialTarget.ToLower()) {
                    "ppe" { $credFile = "$credPath\ppeDomainCreds.xml" }
                    "dev" { $credFile = "$credPath\devDomainCreds.xml" }
                    "admin" { $credFile = "$credPath\windowsCreds.xml" }
                    "cli" { $credFile = "$credPath\cliCreds.xml" }
                    default { $credFile = "$credPath\prdDomainCreds.xml" }
                }
            }
            
            Write-Log "Using credential file: $credFile"
            
            if (Test-Path $credFile) {
                $storedCred = Import-Clixml -Path $credFile
                Write-Log "Successfully loaded credentials from file for user: $($storedCred.UserName)"
                return $storedCred
            } else {
                Write-Log "Credential file not found: $credFile" -Level "WARNING"
                return $null
            }
        } catch {
            Write-Log "Failed to load credentials from file: $($_.Exception.Message)" -Level "WARNING"
            return $null
        }
    }
    
    Write-Log "No stored credentials requested - will use default authentication"
    return $null
}

function ConfigSCCM {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]
        [string]$serverName,
        
        [Parameter(Mandatory=$true)]
        [string]$ManagementPoint,
        
        [Parameter(Mandatory=$true)]
        [string]$SiteCode,
        
        [string]$ClientSourcePath,
        
        [switch]$Force,
        
        [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidUsingPlainTextForPassword', 'CredentialTarget', Justification = 'CredentialTarget is not a password - it specifies which credential file to use')]
        [ValidateSet("ppe", "dev", "admin", "cli", "")]
        [string]$CredentialTarget = ""
    )
    
    Write-Log "Starting remote SCCM client installation on server: $serverName"
    Write-Log "Management Point: $ManagementPoint"
    Write-Log "Site Code: $SiteCode"
    
    # Automatically load credentials from XML files
    Write-Log "Loading stored credentials for remote installation..."
    
    try {
        $credPath = $config.GlobalConfig.CredPath
        $credFile = "$credPath\prdDomainCreds.xml"
        
        if ($CredentialTarget) {
            switch ($CredentialTarget.ToLower()) {
                "ppe" { $credFile = "$credPath\ppeDomainCreds.xml" }
                "dev" { $credFile = "$credPath\devDomainCreds.xml" }
                "admin" { $credFile = "$credPath\windowsCreds.xml" }
                "cli" { $credFile = "$credPath\cliCreds.xml" }
                default { $credFile = "$credPath\prdDomainCreds.xml" }
            }
        }
        
        Write-Log "Using credential file: $credFile"
        
        if (-not (Test-Path $credFile)) {
            throw "Credential file not found: $credFile. This file is required for remote installation."
        }
        
        $user = Import-Clixml -Path $credFile
        Write-Log "Successfully loaded credentials for user: $($user.UserName)"
        
    } catch {
        Write-Log "Failed to load credentials: $($_.Exception.Message)" -Level "ERROR"
        throw "Remote installation cannot proceed without valid credentials. Error: $($_.Exception.Message)"
    }
    
    try {
        $installResult = Invoke-Command -ComputerName $serverName -Credential $user -ScriptBlock {    
            param (
                $managementPoint,
                $siteCode,
                $clientSourcePath,
                $defaultSourcePath,
                $force
            )
            
            # Define local variables for the remote session
            $tempPath = "C:\Temp\ServerInstalls\SCCM"
            $LogPath = "C:\Windows\Temp\SCCMClient_Install_Remote.log"
            
            function Write-RemoteLog {
                param([string]$Message, [string]$Level = "INFO")
                $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
                $logMessage = "[$timestamp] [$Level] $Message"
                Write-Host $logMessage
                Add-Content -Path $LogPath -Value $logMessage -ErrorAction SilentlyContinue
            }
            
            try {
                # Check if SCCM client is already installed
                $ccmExec = Get-Service -Name "CcmExec" -ErrorAction SilentlyContinue
                $ccmSetup = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\SMS\Setup" -ErrorAction SilentlyContinue
                
                if (($ccmExec -or $ccmSetup) -and -not $force) {
                    return @{
                        Success = $false
                        Message = "SCCM client is already installed. Use -Force parameter to reinstall."
                        ExitCode = 1619
                    }
                }
                
                $osVersion = (Get-WmiObject -Class Win32_OperatingSystem).Caption
                Write-RemoteLog "Target OS: $osVersion"
                
                # Find the installer
                $installerPath = $null
                if ($clientSourcePath -and (Test-Path $clientSourcePath)) {
                    $installerPath = $clientSourcePath
                    Write-RemoteLog "Using provided installer: $installerPath"
                } elseif ($defaultSourcePath -and (Test-Path $defaultSourcePath)) {
                    $installerPath = $defaultSourcePath
                    Write-RemoteLog "Using default installer: $installerPath"
                } else {
                    # Try common paths
                    $commonPaths = @(
                        "C:\Windows\ccmsetup\ccmsetup.exe",
                        "C:\Temp\ccmsetup.exe",
                        "$tempPath\ccmsetup.exe"
                    )
                    
                    foreach ($path in $commonPaths) {
                        if (Test-Path $path) {
                            $installerPath = $path
                            Write-RemoteLog "Found installer at: $path"
                            break
                        }
                    }
                }
                
                if (-not $installerPath) {
                    throw "SCCM client installer not found on target system"
                }
                
                # Install SCCM client
                $arguments = @()
                $arguments += "SMSMP=$managementPoint"
                $arguments += "SMSSITECODE=$siteCode"
                $arguments += "SMSCACHESIZE=5120"
                $arguments += "SMSCACHEDIR=`"C:\Windows\ccmcache`""
                $arguments += "CCMHTTPPORT=80"
                $arguments += "CCMHTTPSPORT=443"
                $arguments += "CCMHTTPSSTATE=31"
                $arguments += "CCMFIRSTCERT=1"
                $arguments += "CCMINSTALLDIR=`"C:\Windows\CCM`""
                
                $argumentString = $arguments -join " "
                Write-RemoteLog "Installation command: `"$installerPath`" $argumentString"
                
                $installProcess = Start-Process -FilePath $installerPath -ArgumentList $argumentString -Wait -PassThru -NoNewWindow
                
                if ($installProcess.ExitCode -ne 0) {
                    throw "SCCM client installation failed with exit code: $($installProcess.ExitCode)"
                }
                
                Write-RemoteLog "SCCM client installation completed, waiting for services..."
                
                # Wait for services to start
                $timeout = 300
                $timer = 0
                do {
                    Start-Sleep -Seconds 10
                    $timer += 10
                    $ccmExecCheck = Get-Service -Name "CcmExec" -ErrorAction SilentlyContinue
                    
                    if ($ccmExecCheck -and $ccmExecCheck.Status -eq "Running") {
                        Write-RemoteLog "CcmExec service is running"
                        break
                    }
                    
                    if ($timer -ge $timeout) {
                        Write-RemoteLog "Timeout waiting for CcmExec service" "WARNING"
                        break
                    }
                } while ($true)
                
                return @{
                    Success = $true
                    Message = "SCCM client installation completed successfully"
                    ExitCode = 0
                    OSVersion = $osVersion
                    ManagementPoint = $managementPoint
                    SiteCode = $siteCode
                }
                
            } catch {
                return @{
                    Success = $false
                    Message = "SCCM client installation failed: $($_.Exception.Message)"
                    ExitCode = -1
                    OSVersion = $osVersion
                }
            }
            
        } -ArgumentList $ManagementPoint, $SiteCode, $ClientSourcePath, $defaultSourcePath, $Force.IsPresent
        
        Write-Log "Installation result: $($installResult.Message)"
        if ($installResult.OSVersion) {
            Write-Log "Target server OS: $($installResult.OSVersion)"
        }
        
        if ($installResult.Success) {
            Start-Sleep -Seconds 30
            
            # Verify installation
            $verificationResult = Invoke-Command -ComputerName $serverName -Credential $user -ScriptBlock {
                $ccmExec = Get-Service -Name "CcmExec" -ErrorAction SilentlyContinue
                $ccmSetup = Get-ItemProperty -Path "HKLM:\SOFTWARE\Microsoft\SMS\Setup" -ErrorAction SilentlyContinue
                
                return @{
                    ServiceStatus = if ($ccmExec) { $ccmExec.Status.ToString() } else { "NotFound" }
                    ClientVersion = if ($ccmSetup) { $ccmSetup."Full Version" } else { "Unknown" }
                    ServiceName = if ($ccmExec) { $ccmExec.Name } else { "N/A" }
                }
            }
            
            if ($verificationResult.ServiceStatus -ne "NotFound") {
                Write-Log "SCCM client verification successful - Service status: $($verificationResult.ServiceStatus)"
                Write-Log "Client version: $($verificationResult.ClientVersion)"
            } else {
                Write-Log "SCCM client verification failed - Service not found" -Level "ERROR"
            }
        } else {
            Write-Log "SCCM client installation failed: $($installResult.Message)" -Level "ERROR"
            throw "Remote installation failed: $($installResult.Message)"
        }
        
    }
    catch {
        Write-Log "Remote SCCM installation failed: $($_.Exception.Message)" -Level "ERROR"
        throw $_
    }
}

function Install-SCCMClientRemote {
    <#
    .SYNOPSIS
        Simplified wrapper function for API-driven remote SCCM client installation.
    
    .DESCRIPTION
        This function provides a simplified interface for API calls to install SCCM clients
        on remote servers. It automatically handles credential loading from XML files
        without requiring interactive input.
    
    .PARAMETER ServerName
        The name of the remote server where the SCCM client will be installed.
    
    .PARAMETER ManagementPoint
        The primary SCCM management point FQDN or IP address.
    
    .PARAMETER SiteCode
        The SCCM site code (typically 3 characters).
    
    .PARAMETER ClientSourcePath
        Optional path to the SCCM client installer (ccmsetup.exe).
    
    .PARAMETER CredentialTarget
        Optional credential target to specify which XML credential file to use.
        Valid values: 'ppe', 'dev', 'admin', 'cli'. Defaults to production credentials.
    
    .PARAMETER Force
        Force reinstallation even if client is already installed.
    
    .EXAMPLE
        Install-SCCMClientRemote -ServerName "Server01" -ManagementPoint "sccm01.domain.com" -SiteCode "ABC"
    
    .EXAMPLE
        Install-SCCMClientRemote -ServerName "Server01" -ManagementPoint "sccm01.domain.com" -SiteCode "ABC" -CredentialTarget "admin" -Force
    #>
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]
        [string]$ServerName,
        
        [Parameter(Mandatory=$true)]
        [string]$ManagementPoint,
        
        [Parameter(Mandatory=$true)]
        [string]$SiteCode,
        
        [string]$ClientSourcePath,
        
        [ValidateSet("ppe", "dev", "admin", "cli", "")]
        [string]$CredentialTarget = "",
        
        [switch]$Force
    )
    
    try {
        Write-Log "API Remote SCCM Installation Request: Server=$ServerName, MP=$ManagementPoint, Site=$SiteCode, CredTarget=$CredentialTarget"
        
        # Call the main ConfigSCCM function with the provided parameters
        $params = @{
            serverName = $ServerName
            ManagementPoint = $ManagementPoint
            SiteCode = $SiteCode
        }
        
        if ($ClientSourcePath) {
            $params.ClientSourcePath = $ClientSourcePath
        }
        
        if ($CredentialTarget) {
            $params.CredentialTarget = $CredentialTarget
        }
        
        if ($Force) { $params.Force = $true }
        
        ConfigSCCM @params
        
        Write-Log "API Remote SCCM Installation completed successfully for $ServerName"
        return @{
            Success = $true
            Message = "SCCM client installation completed successfully on $ServerName"
            ServerName = $ServerName
            ManagementPoint = $ManagementPoint
            SiteCode = $SiteCode
        }
        
    } catch {
        $errorMsg = "API Remote SCCM Installation failed for ${ServerName}: $($_.Exception.Message)"
        Write-Log $errorMsg -Level "ERROR"
        return @{
            Success = $false
            Message = $errorMsg
            ServerName = $ServerName
            ManagementPoint = $ManagementPoint
            SiteCode = $SiteCode
            Error = $_.Exception.Message
        }
    }
}

### Main Execution ###
try {
    Write-Log "=== SCCM Client Installation Script Started ==="
    
    # Check if this is a remote installation request
    if ($ServerName) {
        Write-Log "Remote installation requested for server: $ServerName"
        
        # Call the remote installation function directly
        $params = @{
            serverName = $ServerName
            ManagementPoint = $ManagementPoint
            SiteCode = $SiteCode
        }
        
        if ($ClientSourcePath) {
            $params.ClientSourcePath = $ClientSourcePath
        }
        
        if ($CredentialTarget) {
            $params.CredentialTarget = $CredentialTarget
        }
        
        if ($Force) { $params.Force = $true }
        
        ConfigSCCM @params
        
        $message = "SCCM client installation completed successfully on remote server: $ServerName"
        Write-Log $message
        
        if ($OutputJson) {
            $result = New-JsonReturn -Success "true" -Status "REMOTE_INSTALLED" -Message $message -Data @{
                ServerName = $ServerName
                InstallationType = "Remote"
            }
            Write-Output $result
        } else {
            Write-Host $message -ForegroundColor Green
        }
        
        exit 0
    }
    
    # Continue with local installation if no ServerName provided
    Write-Log "Local installation on: $env:COMPUTERNAME"
    
    $isInstalled = Test-SCCMClientInstalled
    
    if ($isInstalled -and -not $Force) {
        $message = "SCCM client is already installed. Use -Force to reinstall."
        Write-Log $message "WARNING"
        
        if ($OutputJson) {
            $result = New-JsonReturn -Success "true" -Status "ALREADY_INSTALLED" -Message $message
            Write-Output $result
        }
        else {
            Write-Host $message -ForegroundColor Yellow
        }
        exit 0
    }
    
    if ($Force -and $isInstalled) {
        Write-Log "Force reinstallation requested, uninstalling existing client..."
        if (-not (Uninstall-SCCMClient)) {
            throw "Failed to uninstall existing SCCM client"
        }
    }
    
    $installerPath = Get-SCCMClientInstaller
    
    $installSuccess = Install-SCCMClient -InstallerPath $installerPath
    
    if ($installSuccess) {
        Test-SCCMClientConfiguration | Out-Null
        
        $message = "SCCM client installation completed successfully"
        Write-Log $message
        
        if ($OutputJson) {
            $result = New-JsonReturn -Success "true" -Status "INSTALLED" -Message $message
            Write-Output $result
        }
        else {
            Write-Host $message -ForegroundColor Green
        }
    }
    else {
        throw "SCCM client installation failed"
    }
}
catch {
    $errorMessage = "SCCM client installation failed: $($_.Exception.Message)"
    Write-Log $errorMessage "ERROR"
    
    if ($OutputJson) {
        $result = New-JsonReturn -Success "false" -Status "FAILED" -Message $errorMessage
        Write-Output $result
    }
    else {
        Write-Host $errorMessage -ForegroundColor Red
    }
    exit 1
}
finally {
    Write-Log "=== SCCM Client Installation Script Completed ==="
}