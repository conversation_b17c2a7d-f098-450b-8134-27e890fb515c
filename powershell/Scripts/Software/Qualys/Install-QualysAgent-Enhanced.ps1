<#
.SYNOPSIS
    Installs the Qualys Cloud Agent on Windows systems with datacenter-specific configurations and remote installation capabilities.

.DESCRIPTION
    This script installs the Qualys Cloud Agent on Windows systems. It performs three sequential operations:
    1. Installs the Qualys Cloud Agent based on datacenter (BDC or CDC)
    2. Installs and imports the required certificate from PEM file
    3. Configures proxy settings based on datacenter location
    
    The script automatically detects and configures settings based on the specified datacenter,
    ensuring proper communication with Qualys cloud services through the appropriate proxy infrastructure.
    
    All configuration values are loaded from the mandatory scriptConfigs.json file. 
    Only the Datacenter parameter and optional switches can be specified.
    The agent source path is automatically determined from the configuration with datacenter substitution.

.PARAMETER Datacenter
    The datacenter location for this server. Valid values are 'BDC' or 'CDC'. This determines which
    ActivationId and proxy configuration will be used.

.PARAMETER Force
    Force reinstallation even if agent is already installed.

.PARAMETER OutputJson
    Return results in JSON format suitable for automation and API integration.

.PARAMETER SkipCertificate
    Skip the certificate installation step.

.PARAMETER SkipProxy
    Skip the proxy configuration step.

.PARAMETER UseStoredCredentials
    Use stored credentials from XML credential files for service accounts.

.PARAMETER CredentialTarget
    The target environment to determine which credential file to use. Valid values: 'ppe', 'dev', 'admin', 'cli'. If not specified, defaults to production domain credentials.

.EXAMPLE
    .\Install-QualysAgent-Enhanced.ps1 -Datacenter "BDC"

.EXAMPLE
    .\Install-QualysAgent-Enhanced.ps1 -Datacenter "CDC" -Force

.EXAMPLE
    .\Install-QualysAgent-Enhanced.ps1 -Datacenter "BDC" -OutputJson

.EXAMPLE
    .\Install-QualysAgent-Enhanced.ps1 -Datacenter "CDC" -SkipCertificate -SkipProxy

.EXAMPLE
    .\Install-QualysAgent-Enhanced.ps1 -Datacenter "BDC" -UseStoredCredentials -CredentialTarget "ppe"

.EXAMPLE
    # Remote installation using ConfigQualys function
    $cred = Get-Credential
    ConfigQualys -serverName "RemoteServer01" -user $cred -Datacenter "BDC" -Force

.NOTES
    File Name   : Install-QualysAgent-Enhanced.ps1
    Author      : Rudi van Zyl
    Requires    : PowerShell 5.0 or later, Administrator privileges
    Requires    : scriptConfigs.json file in same directory (MANDATORY - contains all configurations)
    Compatible  : Windows Server 2012, 2016, 2019, 2022
    
    Version 1.0 - Initial script for Qualys Cloud Agent installation
    Version 2.0 - Enhanced with remote installation, config file support, and improved error handling
    Version 2.1 - Simplified parameters by moving defaults to configuration file
    Version 2.2 - Configuration file now mandatory, removed parameter overrides for config values
    Version 2.3 - Removed AgentSourcePath parameter, all paths now from configuration file only
    
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [ValidateSet("BDC", "CDC")]
    [string]$Datacenter,
    
    [switch]$Force,
    
    [switch]$OutputJson,
    
    [switch]$SkipCertificate,
    
    [switch]$SkipProxy,
    
    [switch]$UseStoredCredentials,
    
    [string]$CredentialTarget
)

#Requires -Version 5
#Requires -RunAsAdministrator

### Global Variables ###
$currentPath = split-path -parent $MyInvocation.MyCommand.Definition

# Try to load configuration file
$configPath = "$currentPath\scriptConfigs.json"
$config = $null
if (Test-Path $configPath) {
    try {
        $config = Get-Content -Path $configPath | ConvertFrom-Json
        Write-Verbose "Configuration loaded from: $configPath"
    }
    catch {
        Write-Warning "Failed to load configuration file: $configPath"
        exit 1
    }
} else {
    Write-Error "Configuration file not found: $configPath. This file is required for operation."
    exit 1
}

# Set configuration references
$qualysConfig = $config.QualysConfig
$datacenterConfig = $qualysConfig.DatacenterConfig

# Use configuration values directly (no parameter overrides)
$CustomerId = $qualysConfig.CustomerId
$WebServiceUri = $qualysConfig.WebServiceUri
$InstallPath = $qualysConfig.InstallPath
$LogPath = $qualysConfig.LogPath
$CertificateSourcePath = $qualysConfig.CertificateSourcePath
$PemFilePath = $qualysConfig.PemFilePath
$SourceServer = $qualysConfig.SourceServer
$SourcePath = $qualysConfig.SourcePath

### Paths ###
if ($config) {
    $credPath = $config.CredPath
    $cliCredsPath = "$credPath\cliCreds.xml"
    $adminCredLocation = "$credPath\windowsCreds.xml"
    $mudCreds = "$credPath\prdDomainCreds.xml"
    $ppeCreds = "$credPath\ppeDomainCreds.xml"
    $devCreds = "$credPath\devDomainCreds.xml"
}

$ErrorActionPreference = "Stop"
$tempPath = $qualysConfig.TempPath
$agentInstaller = $qualysConfig.AgentInstaller

### Functions ###
function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Success,
        
        [Parameter(Mandatory = $true)]
        [string]$Status,
        
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [hashtable]$AdditionalData = @{}
    )
   
    $agentInfo = @{}
    try {
        # Check for Qualys agent service
        $service = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue
        if ($service) {
            $agentInfo.ServiceStatus = $service.Status.ToString()
        }
        
        # Check for installed Qualys software
        $installedAgent = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Qualys*" } -ErrorAction SilentlyContinue
        if ($installedAgent) {
            $agentInfo.AgentName = $installedAgent.Name
            $agentInfo.AgentVersion = $installedAgent.Version
        }
        
        # Check agent installation path
        if (Test-Path $InstallPath) {
            $agentInfo.InstallationPath = $InstallPath
        }
    }
    catch {
        # Ignore errors during data collection
    }
    
    $objectReturn = @{
        computerName        = $env:COMPUTERNAME
        datacenter         = $Datacenter
        customerId         = $CustomerId
        activationId       = $datacenterConfig[$Datacenter].ActivationId
        webServiceUri      = $WebServiceUri
        installationPath   = $InstallPath
        agentInfo          = $agentInfo
        proxyConfiguration = $datacenterConfig[$Datacenter].ProxyServers -join ";"
        timeStamp          = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }
    
    if ($AdditionalData.Count -gt 0) {
        foreach ($key in $AdditionalData.Keys) {
            $objectReturn[$key] = $AdditionalData[$key]
        }
    }
    
    $jsonResponse = @{
        success = $Success
        status  = $Status
        message = $Message
        data    = $objectReturn
    }
    
    return ($jsonResponse | ConvertTo-Json -Depth 4)
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    Write-Host $logMessage
    Add-Content -Path $LogPath -Value $logMessage -ErrorAction SilentlyContinue
}

function Get-QualysCredentials {
    Write-Log "Getting Qualys service credentials..."
    
    if ($UseStoredCredentials) {
        try {
            $credFile = $mudCreds 
            
            if ($CredentialTarget) {
                switch ($CredentialTarget.ToLower()) {
                    "ppe" { $credFile = $ppeCreds }
                    "dev" { $credFile = $devCreds }
                    "admin" { $credFile = $adminCredLocation }
                    "cli" { $credFile = $cliCredsPath }
                    default { $credFile = $mudCreds }
                }
            }
            
            Write-Log "Using credential file: $credFile"
            
            if (Test-Path $credFile) {
                $storedCred = Import-Clixml -Path $credFile
                Write-Log "Successfully loaded credentials from file for user: $($storedCred.UserName)"
                return $storedCred
            } else {
                Write-Log "Credential file not found: $credFile" -Level "WARNING"
                return $null
            }
        } catch {
            Write-Log "Failed to load credentials from file: $($_.Exception.Message)" -Level "WARNING"
            return $null
        }
    }
    
    Write-Log "No stored credentials requested - will use default authentication"
    return $null
}

function Test-QualysPrerequisites {
    Write-Log "Checking Qualys installation prerequisites..."
    
    $currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
    if (-not $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
        throw "This script must be run as Administrator"
    }
    
    $osVersion = (Get-WmiObject -Class Win32_OperatingSystem).Caption
    Write-Log "Operating System: $osVersion"
    
    if ($osVersion -notmatch "Server 2012|Server 2016|Server 2019|Server 2022") {
        Write-Log "Warning: This script is designed for Windows Server 2012, 2016, 2019 and 2022. Detected: $osVersion" -Level "WARNING"
    }
    
    # Check network connectivity to Qualys cloud
    try {
        $uri = [System.Uri]$WebServiceUri
        $testConnection = Test-NetConnection -ComputerName $uri.Host -Port 443 -InformationLevel Quiet -ErrorAction SilentlyContinue
        if ($testConnection) {
            Write-Log "Network connectivity to Qualys cloud service verified"
        } else {
            Write-Log "Warning: Cannot verify network connectivity to Qualys cloud service" -Level "WARNING"
        }
    }
    catch {
        Write-Log "Warning: Could not test network connectivity to Qualys cloud service: $($_.Exception.Message)" -Level "WARNING"
    }
    
    Write-Log "Prerequisites check completed"
}

function Test-QualysAgentInstalled {
    try {
        $service = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue
        $installPath = Test-Path $InstallPath
        
        if ($service -and $installPath) {
            Write-Log "Qualys agent is already installed. Service status: $($service.Status)" "INFO"
            return $true
        }
        return $false
    }
    catch {
        return $false
    }
}

function Get-QualysAgentInstaller {
    try {
        # Use agent source path from configuration with datacenter substitution
        $AgentSourcePath = $qualysConfig.AgentSourcePath.Replace("[Datacenter]", $Datacenter)
        
        # Verify agent installer exists
        if (-not (Test-Path $AgentSourcePath)) {
            throw "Qualys agent installer not found at: $AgentSourcePath"
        }
        
        Write-Log "Using Qualys agent installer: $AgentSourcePath"
        return $AgentSourcePath
    }
    catch {
        Write-Log "Error getting Qualys agent installer: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Install-QualysAgent {
    Write-Log "Starting Qualys Cloud Agent installation for datacenter: $Datacenter" "INFO"
    
    try {
        $installerPath = Get-QualysAgentInstaller
        
        # Copy installer to temp directory
        $localInstallerPath = Join-Path $tempPath $agentInstaller
        Copy-Item $installerPath $localInstallerPath -Force
        Write-Log "Copied agent installer to: $localInstallerPath" "INFO"
        
        # Prepare installation command with datacenter-specific parameters
        $activationId = $datacenterConfig[$Datacenter].ActivationId
        $installArgs = @(
            "CustomerId={`"$CustomerId`"}",
            "ActivationId={`"$activationId`"}",
            "WebServiceUri=`"$WebServiceUri`""
        )
        
        $installCommand = "`"$localInstallerPath`" " + ($installArgs -join " ")
        Write-Log "Installation command: $installCommand" "INFO"
        
        # Execute installation
        Write-Log "Installing Qualys Cloud Agent..." "INFO"
        $process = Start-Process -FilePath $localInstallerPath -ArgumentList $installArgs -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Log "Qualys Cloud Agent installation completed successfully" "INFO"
            return $true
        } else {
            throw "Agent installation failed with exit code: $($process.ExitCode)"
        }
    }
    catch {
        Write-Log "Error during agent installation: $($_.Exception.Message)" "ERROR"
        throw
    }
    finally {
        # Clean up temp files
        $localInstallerPath = Join-Path $tempPath $agentInstaller
        if (Test-Path $localInstallerPath) {
            Remove-Item $localInstallerPath -Force -ErrorAction SilentlyContinue
        }
    }
}

function Install-QualysCertificate {
    if ($SkipCertificate) {
        Write-Log "Skipping certificate installation as requested" "INFO"
        return $true
    }
    
    Write-Log "Starting Qualys certificate installation" "INFO"
    
    try {
        # Use the configured paths (already set from config defaults)
        $certPath = $CertificateSourcePath
        $pemPath = $PemFilePath
        
        # Verify certificate MSI exists
        if (-not (Test-Path $certPath)) {
            throw "Certificate MSI not found at: $certPath"
        }
        
        # Verify PEM file exists
        if (-not (Test-Path $pemPath)) {
            throw "PEM certificate file not found at: $pemPath"
        }
        
        # Install certificate MSI
        Write-Log "Installing certificate MSI..." "INFO"
        $msiArgs = @("/i", "`"$certPath`"", "/quiet", "/norestart")
        $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $msiArgs -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -ne 0) {
            throw "Certificate MSI installation failed with exit code: $($process.ExitCode)"
        }
        
        Write-Log "Certificate MSI installed successfully" "INFO"
        
        # Import PEM certificate to Trusted Root store
        Write-Log "Importing PEM certificate to Trusted Root store..." "INFO"
        
        # Copy PEM file to temp directory
        $localPemPath = Join-Path $tempPath "Appliance-Certificate.pem"
        Copy-Item $pemPath $localPemPath -Force
        
        # Import certificate using certutil
        $certutilArgs = @("-addstore", "-f", "Root", "`"$localPemPath`"")
        $process = Start-Process -FilePath "certutil.exe" -ArgumentList $certutilArgs -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Log "PEM certificate imported successfully to Trusted Root store" "INFO"
        } else {
            throw "PEM certificate import failed with exit code: $($process.ExitCode)"
        }
        
        return $true
    }
    catch {
        Write-Log "Error during certificate installation: $($_.Exception.Message)" "ERROR"
        throw
    }
    finally {
        # Clean up temp PEM file
        $localPemPath = Join-Path $tempPath "Appliance-Certificate.pem"
        if (Test-Path $localPemPath) {
            Remove-Item $localPemPath -Force -ErrorAction SilentlyContinue
        }
    }
}

function Set-QualysProxy {
    if ($SkipProxy) {
        Write-Log "Skipping proxy configuration as requested" "INFO"
        return $true
    }
    
    Write-Log "Starting Qualys proxy configuration for datacenter: $Datacenter" "INFO"
    
    try {
        # Verify Qualys agent is installed
        if (-not (Test-Path $InstallPath)) {
            throw "Qualys agent installation directory not found: $InstallPath"
        }
        
        $qualysProxyPath = Join-Path $InstallPath "QualysProxy.exe"
        if (-not (Test-Path $qualysProxyPath)) {
            throw "QualysProxy.exe not found at: $qualysProxyPath"
        }
        
        # Get datacenter-specific proxy servers
        $proxyServers = $datacenterConfig[$Datacenter].ProxyServers
        $proxyString = $proxyServers -join ";"
        
        Write-Log "Configuring proxy servers: $proxyString" "INFO"
        
        # Set working directory and execute proxy configuration
        Push-Location $InstallPath
        try {
            $proxyArgs = @("/u", $proxyString)
            $process = Start-Process -FilePath $qualysProxyPath -ArgumentList $proxyArgs -Wait -PassThru -NoNewWindow
            
            if ($process.ExitCode -eq 0) {
                Write-Log "Proxy configuration completed successfully" "INFO"
                return $true
            } else {
                throw "Proxy configuration failed with exit code: $($process.ExitCode)"
            }
        }
        finally {
            Pop-Location
        }
    }
    catch {
        Write-Log "Error during proxy configuration: $($_.Exception.Message)" "ERROR"
        throw
    }
}

function Test-QualysAgentHealth {
    Write-Log "Performing Qualys agent health check..." "INFO"
    
    try {
        # Check service status
        $service = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue
        if (-not $service) {
            Write-Log "Qualys agent service not found" "WARNING"
            return $false
        }
        
        Write-Log "Qualys agent service status: $($service.Status)" "INFO"
        
        if ($service.Status -ne "Running") {
            Write-Log "Attempting to start Qualys agent service..." "INFO"
            Start-Service -Name "QualysAgent" -ErrorAction Stop
            Start-Sleep -Seconds 5
            
            $service = Get-Service -Name "QualysAgent"
            if ($service.Status -eq "Running") {
                Write-Log "Qualys agent service started successfully" "INFO"
            } else {
                Write-Log "Failed to start Qualys agent service" "ERROR"
                return $false
            }
        }
        
        # Verify installation directory
        if (Test-Path $InstallPath) {
            Write-Log "Qualys agent installation directory verified: $InstallPath" "INFO"
        } else {
            Write-Log "Qualys agent installation directory not found: $InstallPath" "WARNING"
            return $false
        }
        
        return $true
    }
    catch {
        Write-Log "Error during health check: $($_.Exception.Message)" "ERROR"
        return $false
    }
}

function ConfigQualys {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]
        [string]$serverName,
        
        [Parameter(Mandatory=$true)]
        [PSCredential]$user,
        
        [Parameter(Mandatory=$true)]
        [ValidateSet("BDC", "CDC")]
        [string]$Datacenter,
        
        [switch]$Force,
        
        [switch]$SkipCertificate,
        
        [switch]$SkipProxy
    )
    
    Write-Log "Starting remote Qualys agent installation on server: $serverName"
    Write-Log "Source: \\$SourceServer\$SourcePath"
    Write-Log "Datacenter: $Datacenter"
    
    try {
        $installResult = Invoke-Command -ComputerName $serverName -Credential $user -ScriptBlock {    
            param (
                $datacenter,
                $sourceServer,
                $sourcePath,
                $customerId,
                $webServiceUri,
                $installPath,
                $force,
                $skipCertificate,
                $skipProxy,
                $datacenterConfigJson
            )
            
            # Convert the datacenter configuration back from JSON
            $datacenterConfig = $datacenterConfigJson | ConvertFrom-Json -AsHashtable
            
            # Check if agent is already installed
            $service = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue
            $existingInstall = Test-Path $installPath
            
            if (($service -or $existingInstall) -and -not $force) {
                return @{
                    Success = $false
                    Message = "Qualys agent is already installed. Use -Force parameter to reinstall."
                    ExitCode = 1619
                }
            }
            
            $osVersion = (Get-WmiObject -Class Win32_OperatingSystem).Caption
            
            # Phase 1: Install Qualys Agent
            try {
                $agentUncPath = "\\$sourceServer\$sourcePath\$datacenter\QualysCloudAgent.exe"
                
                if (-not (Test-Path $agentUncPath)) {
                    throw "Qualys agent installer not found at: $agentUncPath"
                }
                
                # Copy to local temp
                $tempPath = "C:\Windows\Temp"
                $localAgentPath = Join-Path $tempPath "QualysCloudAgent.exe"
                Copy-Item $agentUncPath $localAgentPath -Force
                
                # Prepare installation command
                $activationId = $datacenterConfig[$datacenter].ActivationId
                $installArgs = @(
                    "CustomerId={`"$customerId`"}",
                    "ActivationId={`"$activationId`"}",
                    "WebServiceUri=`"$webServiceUri`""
                )
                
                # Execute installation
                $process = Start-Process -FilePath $localAgentPath -ArgumentList $installArgs -Wait -PassThru -NoNewWindow
                
                if ($process.ExitCode -ne 0) {
                    throw "Agent installation failed with exit code: $($process.ExitCode)"
                }
                
                # Clean up
                Remove-Item $localAgentPath -Force -ErrorAction SilentlyContinue
                
            } catch {
                return @{
                    Success = $false
                    Message = "Phase 1 (Agent Installation) failed: $($_.Exception.Message)"
                    ExitCode = -1
                    OSVersion = $osVersion
                }
            }
            
            # Phase 2: Install Certificate (if not skipped)
            if (-not $skipCertificate) {
                try {
                    $certUncPath = "\\$sourceServer\$sourcePath\Certificate\WIN.msi"
                    $pemUncPath = "\\$sourceServer\$sourcePath\Certificate\Appliance-Certificate.pem"
                    
                    if (Test-Path $certUncPath) {
                        # Install certificate MSI
                        $msiArgs = @("/i", "`"$certUncPath`"", "/quiet", "/norestart")
                        $certProcess = Start-Process -FilePath "msiexec.exe" -ArgumentList $msiArgs -Wait -PassThru -NoNewWindow
                        
                        if ($certProcess.ExitCode -ne 0) {
                            throw "Certificate MSI installation failed with exit code: $($certProcess.ExitCode)"
                        }
                    }
                    
                    if (Test-Path $pemUncPath) {
                        # Import PEM certificate
                        $localPemPath = Join-Path $tempPath "Appliance-Certificate.pem"
                        Copy-Item $pemUncPath $localPemPath -Force
                        
                        $certutilArgs = @("-addstore", "-f", "Root", "`"$localPemPath`"")
                        $pemProcess = Start-Process -FilePath "certutil.exe" -ArgumentList $certutilArgs -Wait -PassThru -NoNewWindow
                        
                        if ($pemProcess.ExitCode -ne 0) {
                            throw "PEM certificate import failed with exit code: $($pemProcess.ExitCode)"
                        }
                        
                        # Clean up
                        Remove-Item $localPemPath -Force -ErrorAction SilentlyContinue
                    }
                    
                } catch {
                    return @{
                        Success = $false
                        Message = "Phase 2 (Certificate Installation) failed: $($_.Exception.Message)"
                        ExitCode = -2
                        OSVersion = $osVersion
                    }
                }
            }
            
            # Phase 3: Configure Proxy (if not skipped)
            if (-not $skipProxy) {
                try {
                    # Wait for agent to be fully installed
                    Start-Sleep -Seconds 30
                    
                    $qualysProxyPath = Join-Path $installPath "QualysProxy.exe"
                    if (Test-Path $qualysProxyPath) {
                        $proxyServers = $datacenterConfig[$datacenter].ProxyServers
                        $proxyString = $proxyServers -join ";"
                        
                        Push-Location $installPath
                        try {
                            $proxyArgs = @("/u", $proxyString)
                            $proxyProcess = Start-Process -FilePath $qualysProxyPath -ArgumentList $proxyArgs -Wait -PassThru -NoNewWindow
                            
                            if ($proxyProcess.ExitCode -ne 0) {
                                throw "Proxy configuration failed with exit code: $($proxyProcess.ExitCode)"
                            }
                        }
                        finally {
                            Pop-Location
                        }
                    }
                    
                } catch {
                    return @{
                        Success = $false
                        Message = "Phase 3 (Proxy Configuration) failed: $($_.Exception.Message)"
                        ExitCode = -3
                        OSVersion = $osVersion
                    }
                }
            }
            
            return @{
                Success = $true
                Message = "Qualys agent installation completed successfully for datacenter $datacenter"
                ExitCode = 0
                OSVersion = $osVersion
                Datacenter = $datacenter
                ActivationId = $datacenterConfig[$datacenter].ActivationId
            }
            
        } -ArgumentList $Datacenter, $SourceServer, $SourcePath, $CustomerId, $WebServiceUri, $InstallPath, $Force.IsPresent, $SkipCertificate.IsPresent, $SkipProxy.IsPresent, ($datacenterConfig | ConvertTo-Json -Depth 3)
        
        Write-Log "Installation result: $($installResult.Message)"
        if ($installResult.OSVersion) {
            Write-Log "Target server OS: $($installResult.OSVersion)"
        }
        
        if ($installResult.Success) {
            # Wait for services to start and verify installation
            Start-Sleep -Seconds 60
            
            $verificationResult = Invoke-Command -ComputerName $serverName -Credential $user -ScriptBlock {
                param($installPath)
                
                $service = Get-Service -Name "QualysAgent" -ErrorAction SilentlyContinue
                $pathExists = Test-Path $installPath
                
                return @{
                    ServiceStatus = if ($service) { $service.Status.ToString() } else { "NotFound" }
                    InstallPathExists = $pathExists
                    ServiceName = if ($service) { $service.Name } else { "N/A" }
                }
            } -ArgumentList $InstallPath
            
            if ($verificationResult.ServiceStatus -ne "NotFound") {
                Write-Log "Qualys agent verification successful - Service status: $($verificationResult.ServiceStatus)"
                Write-Log "Installation path exists: $($verificationResult.InstallPathExists)"
            } else {
                Write-Log "Qualys agent verification failed - Service not found" -Level "ERROR"
            }
        } else {
            Write-Log "Qualys agent installation failed: $($installResult.Message)" -Level "ERROR"
            throw "Remote installation failed: $($installResult.Message)"
        }
        
    }
    catch {
        Write-Log "Remote Qualys installation failed: $($_.Exception.Message)" -Level "ERROR"
        throw $_
    }
}

### Main Execution ###
try {
    Write-Log "=== Qualys Cloud Agent Installation Script Started ===" "INFO"
    Write-Log "Target datacenter: $Datacenter" "INFO"
    Write-Log "Customer ID: $CustomerId" "INFO"
    Write-Log "Activation ID: $($datacenterConfig[$Datacenter].ActivationId)" "INFO"
    Write-Log "Web Service URI: $WebServiceUri" "INFO"
    
    # Step 1: Check prerequisites
    Test-QualysPrerequisites
    
    # Check if agent is already installed
    if (Test-QualysAgentInstalled -and -not $Force) {
        $message = "Qualys agent is already installed. Use -Force to reinstall."
        Write-Log $message "INFO"
        
        if ($OutputJson) {
            Write-Output (New-JsonReturn -Success "true" -Status "Already Installed" -Message $message)
        } else {
            Write-Host $message -ForegroundColor Yellow
        }
        exit 0
    }
    
    # Phase 1: Install Qualys Cloud Agent
    Write-Log "=== Phase 1: Installing Qualys Cloud Agent ===" "INFO"
    $agentInstalled = Install-QualysAgent
    
    if (-not $agentInstalled) {
        throw "Failed to install Qualys Cloud Agent"
    }
    
    # Phase 2: Install Certificate
    Write-Log "=== Phase 2: Installing Qualys Certificate ===" "INFO"
    $certificateInstalled = Install-QualysCertificate
    
    if (-not $certificateInstalled) {
        throw "Failed to install Qualys certificate"
    }
    
    # Phase 3: Configure Proxy
    Write-Log "=== Phase 3: Configuring Qualys Proxy ===" "INFO"
    $proxyConfigured = Set-QualysProxy
    
    if (-not $proxyConfigured) {
        throw "Failed to configure Qualys proxy"
    }
    
    # Final health check
    Write-Log "=== Final Health Check ===" "INFO"
    $healthCheck = Test-QualysAgentHealth
    
    if ($healthCheck) {
        $message = "Qualys Cloud Agent installation completed successfully for datacenter $Datacenter"
        Write-Log $message "INFO"
        
        if ($OutputJson) {
            Write-Output (New-JsonReturn -Success "true" -Status "Completed" -Message $message)
        } else {
            Write-Host $message -ForegroundColor Green
        }
    } else {
        $message = "Qualys Cloud Agent installation completed with warnings. Please check the service status."
        Write-Log $message "WARNING"
        
        if ($OutputJson) {
            Write-Output (New-JsonReturn -Success "true" -Status "Completed with Warnings" -Message $message)
        } else {
            Write-Host $message -ForegroundColor Yellow
        }
    }
}
catch {
    $errorMessage = "Qualys Cloud Agent installation failed: $($_.Exception.Message)"
    Write-Log $errorMessage "ERROR"
    
    if ($OutputJson) {
        Write-Output (New-JsonReturn -Success "false" -Status "Failed" -Message $errorMessage)
    } else {
        Write-Host $errorMessage -ForegroundColor Red
    }
    
    exit 1
}
finally {
    Write-Log "=== Qualys Cloud Agent Installation Script Completed ===" "INFO"
}
