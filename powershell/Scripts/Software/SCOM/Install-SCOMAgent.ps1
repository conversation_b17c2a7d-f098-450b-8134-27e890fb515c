<#
.SYNOPSIS
    Installs the <PERSON>OM (System Center Operations Manager) agent on Windows Server 2016, 2019 and 2022.

.DESCRIPTION
    This script installs the SCOM agent on Windows Server systems. It downloads the agent installer,
    performs silent installation, configures the agent with management group settings, and validates the installation.
    Supports both shared servers and SQL servers with automatic management group selection based on application type.
    Configuration values are loaded from scriptConfigs.json for consistent environment-specific defaults.

.PARAMETER ManagementServer
    The primary SCOM management server FQDN or IP address. If not provided, uses value from scriptConfigs.json.

.PARAMETER ManagementGroup
    The name of the SCOM management group. If not provided, uses default value from scriptConfigs.json.
    SQL servers automatically use configured SQL management group when AppType is set to "MSSQL".

.PARAMETER ServerName
    Optional remote server name. If provided, the script will install the SCOM agent on the remote server instead of locally.

.PARAMETER InstallPath
    The installation path for the SCOM agent. Default value is loaded from scriptConfigs.json.

.PARAMETER AgentSourcePath
    The path to the SCOM agent installer (MOMAgent.msi). If not provided, the script will attempt to download it.

.PARAMETER ActionAccount
    The action account to use for the agent (optional). Format: DOMAIN\Username

.PARAMETER ActionAccountPassword
    The password for the action account (optional).

.PARAMETER DownloadUrl
    The URL to download the SCOM agent installer from (optional).

.PARAMETER LogPath
    The path where installation logs will be written. Default value is loaded from scriptConfigs.json.

.PARAMETER Force
    Force reinstallation even if agent is already installed.

.PARAMETER OutputJson
    Return results in JSON format suitable for automation and API integration.

.PARAMETER UseStoredCredentials
    Use stored credentials from XML credential files for the action account.

.PARAMETER CredentialTarget
    The target environment to determine which credential file to use. Valid values: 'ppe', 'dev', 'admin', 'cli'. If not specified, defaults to production domain credentials or auto-detects from ManagementServer FQDN.

.PARAMETER AppType
    The application type to determine the appropriate management group. Valid values are 'Shared' (default) or 'MSSQL'. SQL servers will use a dedicated SQL management group.

.PARAMETER ManagementPort
    The SCOM management server port number. Default value is loaded from scriptConfigs.json.

.EXAMPLE
    # Basic installation using configuration defaults
    .\Install-SCOMAgent.ps1 -ManagementServer "SRV009365.mud.internal.co.za" -ManagementGroup "ITISSOPSMGR_2019"

.EXAMPLE
    # Installation with custom agent source path
    .\Install-SCOMAgent.ps1 -ManagementServer "SRV009365.mud.internal.co.za" -ManagementGroup "ITISSOPSMGR_2019" -AgentSourcePath "\\server\share\MOMAgent.msi"

.EXAMPLE
    # Installation with JSON output (config defaults for paths)
    .\Install-SCOMAgent.ps1 -ManagementServer "SRV009365.mud.internal.co.za" -ManagementGroup "ITISSOPSMGR_2019" -AgentSourcePath "C:\Temp\MOMAgent.msi" -OutputJson

.EXAMPLE
    .\Install-SCOMAgent.ps1 -ManagementServer "SRV009365.mud.internal.co.za" -ManagementGroup "ITISSOPSMGR_2019" -AgentSourcePath "C:\Temp\MOMAgent.msi" -UseStoredCredentials -CredentialTarget "ppe"

.EXAMPLE
    .\Install-SCOMAgent.ps1 -ManagementServer "SRV009365.mud.internal.co.za" -ManagementGroup "ITISSOPSMGR_2019" -AgentSourcePath "C:\Temp\MOMAgent.msi" -AppType "MSSQL"

.EXAMPLE
    .\Install-SCOMAgent.ps1 -ManagementServer "SRV009365.mud.internal.co.za" -ManagementGroup "ITISSOPSMGR_2019" -AppType "MSSQL" -ManagementPort 5723

.EXAMPLE
    # Remote installation - automatically uses stored credentials
    .\Install-SCOMAgent.ps1 -ManagementServer "SRV009365.mud.internal.co.za" -ManagementGroup "ITISSOPSMGR_2019" -ServerName "RemoteServer01" -Force

.EXAMPLE
    # Remote installation with specific credential target
    .\Install-SCOMAgent.ps1 -ManagementServer "SRV009365.mud.internal.co.za" -ManagementGroup "ITISSOPSMGR_2019" -ServerName "RemoteServer01" -CredentialTarget "admin" -AppType "MSSQL"

.EXAMPLE
    # Remote installation using ConfigSCOM function for Shared servers
    $cred = Get-Credential
    ConfigSCOM -serverName "RemoteServer01" -user $cred -ManagementServer "SRV009365.mud.internal.co.za" -ManagementGroup "ITISSOPSMGR_2019" -AppType "Shared" -Force

.EXAMPLE
    # Remote installation using ConfigSCOM function for SQL servers (will resolve to "SGT - SQL Server Management")
    $cred = Get-Credential
    ConfigSCOM -serverName "SQLServer01" -user $cred -ManagementServer "SRV009365.mud.internal.co.za" -ManagementGroup "ITISSOPSMGR_2019" -AppType "MSSQL" -Force

.NOTES
    File Name   : Install-SCOMAgent.ps1
    Author      : Rudi van Zyl
    Requires    : PowerShell 5.0 or later, Administrator privileges
    Requires    : scriptConfigs.json file in same directory
    Compatible  : Windows Server 2016, 2019 and 2022
    
    Version 1.0 - Initial script for SCOM agent installation
    Version 1.1 - Updated to use standardized config file and credential system
    Version 1.2 - Added remote installation capabilities, API-friendly JSON returns, and ServerName parameter
    Version 1.3 - Moved configuration values to scriptConfigs.json for centralized management
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $true)]
    [string]$ManagementServer,
    
    [Parameter(Mandatory = $true)]
    [string]$ManagementGroup,
    
    [string]$ServerName,
    
    [string]$InstallPath,
    
    [string]$AgentSourcePath,
    
    [string]$ActionAccount,
    
    [SecureString]$ActionAccountPassword,
    
    [string]$DownloadUrl,
    
    [string]$LogPath,
    
    [switch]$Force,
    
    [switch]$OutputJson,
    
    [switch]$UseStoredCredentials,
    
    [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidUsingPlainTextForPassword', 'CredentialTarget', Justification = 'CredentialTarget is not a password - it specifies which credential file to use')]
    [string]$CredentialTarget,
    
    [ValidateSet("Shared", "MSSQL")]
    [string]$AppType = "Shared",
    
    [int]$ManagementPort
)

#Requires -Version 5
#Requires -RunAsAdministrator

### Global Variables ###
$currentPath = split-path -parent $MyInvocation.MyCommand.Definition
$mainConfigPath = Join-Path (Split-Path (Split-Path (Split-Path $currentPath -Parent) -Parent) -Parent) "Configs\scriptConfigs.json"
$config = Get-Content -Path $mainConfigPath | ConvertFrom-Json

### Paths ###
$credPath = $config.GlobalConfig.CredPath
$cliCredsPath = "$credPath\cliCreds.xml"
$adminCredLocation = "$credPath\windowsCreds.xml"
$mudCreds = "$credPath\prdDomainCreds.xml"
$ppeCreds = "$credPath\ppeDomainCreds.xml"
$devCreds = "$credPath\devDomainCreds.xml"

### Script Variables ###
$ErrorActionPreference = "Stop"
$tempPath = $config.SCOMConfig.SCOMTempPath
$agentInstaller = $config.SCOMConfig.SCOMAgentInstaller

### SCOM Configuration Settings - Loaded from scriptConfigs.json ###
# Default source server containing SCOM installation files
$defaultSourceServer = $config.SCOMConfig.SCOMSourceServer

# Default path to the SCOM agent MSI file on the source server
$defaultSourcePath = $config.SCOMConfig.SCOMAgentPath

### Set default values from config if not provided ###
if (-not $InstallPath) {
    $InstallPath = $config.SCOMConfig.SCOMDefaultInstallPath
}

if (-not $LogPath) {
    $LogPath = $config.SCOMConfig.SCOMDefaultLogPath
}

if (-not $ManagementPort) {
    $ManagementPort = $config.SCOMConfig.SCOMManagementPort
}

### Functions ###
function New-JsonReturn {
    [CmdletBinding()]
    param(
        [Parameter(Mandatory = $true)]
        [string]$Success,
        
        [Parameter(Mandatory = $true)]
        [string]$Status,
        
        [Parameter(Mandatory = $true)]
        [string]$Message,
        
        [Parameter(Mandatory = $false)]
        [hashtable]$Data = @{}
    )
   
    $agentInfo = @{}
    try {
        $installedAgent = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Microsoft Monitoring Agent*" } -ErrorAction SilentlyContinue
        if ($installedAgent) {
            $agentInfo.AgentName = $installedAgent.Name
            $agentInfo.AgentVersion = $installedAgent.Version
        }
        
        $service = Get-Service -Name $config.SCOMConfig.SCOMServiceName -ErrorAction SilentlyContinue
        if ($service) {
            $agentInfo.ServiceStatus = $service.Status.ToString()
        }
    }
    catch {
        # Ignore errors during data collection
    }
    
    $objectReturn = @{
        computerName     = $env:COMPUTERNAME
        managementServer = $ManagementServer
        managementPort   = $ManagementPort
        managementGroup  = if ($script:actualManagementGroup) { $script:actualManagementGroup } else { $ManagementGroup }
        baseManagementGroup = $ManagementGroup
        appType          = $AppType
        installationPath = $InstallPath
        agentInfo        = $agentInfo
        timeStamp        = (Get-Date -Format "yyyy-MM-dd HH:mm:ss")
    }
    
    if ($Data.Count -gt 0) {
        foreach ($key in $Data.Keys) {
            $objectReturn[$key] = $Data[$key]
        }
    }
    
    $jsonResponse = @{
        success = $Success
        status  = $Status
        message = $Message
        data    = $objectReturn
    }
    
    return ($jsonResponse | ConvertTo-Json -Depth 4)
}

function Write-Log {
    param(
        [string]$Message,
        [string]$Level = "INFO"
    )
    
    $timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $logMessage = "[$timestamp] [$Level] $Message"
    
    Write-Host $logMessage
    Add-Content -Path $LogPath -Value $logMessage -ErrorAction SilentlyContinue
}

function Get-SCOMCredentials {
    Write-Log "Getting SCOM action account credentials..."
    
    if ($UseStoredCredentials) {
        try {
            $credFile = $mudCreds 
            
            if ($CredentialTarget) {
                switch ($CredentialTarget.ToLower()) {
                    "ppe" { $credFile = $ppeCreds }
                    "dev" { $credFile = $devCreds }
                    "admin" { $credFile = $adminCredLocation }
                    "cli" { $credFile = $cliCredsPath }
                    default { $credFile = $mudCreds }
                }
            } else {
                # Try to determine environment from ManagementServer FQDN
                if ($ManagementServer -match 'ppe|test') {
                    $credFile = $ppeCreds
                } elseif ($ManagementServer -match 'dev') {
                    $credFile = $devCreds
                } else {
                    $credFile = $mudCreds
                }
            }
            
            Write-Log "Using credential file: $credFile"
            
            if (Test-Path $credFile) {
                $storedCred = Import-Clixml -Path $credFile
                Write-Log "Successfully loaded credentials from file for user: $($storedCred.UserName)"
                return @{
                    Username = $storedCred.UserName
                    Password = $storedCred.Password
                }
            } else {
                Write-Log "Credential file not found: $credFile" -Level "WARNING"
                return $null
            }
        } catch {
            Write-Log "Failed to load credentials from file: $($_.Exception.Message)" -Level "WARNING"
            return $null
        }
    }
    
    if ($ActionAccount -and $ActionAccountPassword) {
        Write-Log "Using provided action account credentials"
        return @{
            Username = $ActionAccount
            Password = $ActionAccountPassword
        }
    }
    
    Write-Log "No action account credentials provided - will use computer account"
    return $null
}

function Get-SCOMManagementGroup {
    param(
        [string]$BaseManagementGroup,
        [string]$ApplicationType
    )
    
    Write-Log "Determining management group for application type: $ApplicationType"
    
    switch ($ApplicationType.ToUpper()) {
        "MSSQL" {
            if ($BaseManagementGroup -eq $config.SCOMConfig.SCOMDefaultManagementGroup) {
                $mgGroup = $config.SCOMConfig.SCOMSQLManagementGroup
                Write-Log "Using environment-specific SQL management group: $mgGroup"
                return $mgGroup
            } else {
                $mgGroup = "${BaseManagementGroup}_SQL"
                Write-Log "Using SQL-specific management group: $mgGroup"
                return $mgGroup
            }
        }
        "SHARED" {
            Write-Log "Using shared/general management group: $BaseManagementGroup"
            return $BaseManagementGroup
        }
        default {
            Write-Log "Unknown application type '$ApplicationType', defaulting to shared management group: $BaseManagementGroup" -Level "WARNING"
            return $BaseManagementGroup
        }
    }
}

function Test-Prerequisites {
    Write-Log "Checking prerequisites..."
    
    $currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
    if (-not $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
        throw "This script must be run as Administrator"
    }
    
    $osVersion = (Get-WmiObject -Class Win32_OperatingSystem).Caption
    Write-Log "Operating System: $osVersion"
    
    $supportedVersionsPattern = ($config.SCOMConfig.SCOMSupportedOSVersions -join "|")
    if ($osVersion -notmatch $supportedVersionsPattern) {
        $supportedVersionsList = $config.SCOMConfig.SCOMSupportedOSVersions -join ", "
        Write-Log "Warning: This script is designed for $supportedVersionsList. Detected: $osVersion" -Level "WARNING"
    }
    
    $existingAgent = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Microsoft Monitoring Agent*" }
    if ($existingAgent -and -not $Force) {
        throw "SCOM Agent is already installed. Use -Force parameter to reinstall."
    }
    elseif ($existingAgent -and $Force) {
        Write-Log "Existing SCOM Agent found. Force parameter specified - will proceed with reinstallation." -Level "WARNING"
    }
    
    Write-Log "Prerequisites check completed successfully"
}

function Get-SCOMAgentInstaller {
    if ($AgentSourcePath) {
        if (Test-Path $AgentSourcePath) {
            Write-Log "Using provided agent installer: $AgentSourcePath"
            return $AgentSourcePath
        }
        else {
            throw "Specified agent source path does not exist: $AgentSourcePath"
        }
    }
    
    $installerPath = Join-Path $tempPath $agentInstaller
    
    if ($DownloadUrl) {
        Write-Log "Downloading SCOM agent from: $DownloadUrl"
        try {
            Invoke-WebRequest -Uri $DownloadUrl -OutFile $installerPath -UseBasicParsing
            Write-Log "Agent installer downloaded successfully to: $installerPath"
        }
        catch {
            throw "Failed to download SCOM agent installer: $($_.Exception.Message)"
        }
    }
    else {
        throw "No agent source specified. Please provide either -AgentSourcePath or -DownloadUrl parameter."
    }
    
    return $installerPath
}

function Install-SCOMAgent {
    param(
        [string]$InstallerPath,
        [string]$ResolvedManagementGroup
    )
    
    Write-Log "Installing SCOM Agent..."
    
    $credentials = Get-SCOMCredentials
    
    $installArgs = @(
        "/i `"$InstallerPath`"",
        "/quiet",
        "/norestart",
        "MANAGEMENT_GROUP=`"$ResolvedManagementGroup`"",
        "MANAGEMENT_SERVER_DNS=`"$ManagementServer`"",
        "MANAGEMENT_SERVER_PORT=`"$ManagementPort`"",
        "ACTIONS_USE_COMPUTER_ACCOUNT=1"
    )
    
    if ($credentials) {
        $installArgs += "ACTIONS_USE_COMPUTER_ACCOUNT=0"
        $installArgs += "ACTIONSDOMAIN=`"$($credentials.Username.Split('\')[0])`""
        $installArgs += "ACTIONSUSER=`"$($credentials.Username.Split('\')[1])`""
        
        if ($credentials.Password -is [SecureString]) {
            $plainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($credentials.Password))
        } else {
            $plainPassword = $credentials.Password
        }
        
        $installArgs += "ACTIONSPASSWORD=`"$plainPassword`""
        Write-Log "Configuring with action account: $($credentials.Username)"
    } else {
        Write-Log "Using computer account for agent actions"
    }
    
    if ($InstallPath -ne $config.SCOMConfig.SCOMDefaultInstallPath) {
        $installArgs += "INSTALLDIR=`"$InstallPath`""
    }
    
    $installCmd = "msiexec.exe $($installArgs -join ' ') /l*v `"$LogPath`""
    Write-Log "Installation command: $installCmd"
    
    try {
        $process = Start-Process -FilePath "msiexec.exe" -ArgumentList ($installArgs -join ' '), "/l*v", "`"$LogPath`"" -Wait -PassThru -NoNewWindow
        
        if ($process.ExitCode -eq 0) {
            Write-Log "SCOM Agent installation completed successfully"
        }
        elseif ($process.ExitCode -eq 3010) {
            Write-Log "SCOM Agent installation completed successfully. Reboot required." -Level "WARNING"
        }
        else {
            throw "SCOM Agent installation failed with exit code: $($process.ExitCode)"
        }
    }
    catch {
        throw "Failed to install SCOM Agent: $($_.Exception.Message)"
    }
}

function Set-SCOMAgentConfiguration {
    param([string]$ResolvedManagementGroup)
    
    Write-Log "Configuring SCOM Agent..."
    
    $maxWaitTime = 300
    $waitTime = 0
    $serviceName = $config.SCOMConfig.SCOMServiceName
    
    do {
        Start-Sleep -Seconds 10
        $waitTime += 10
        $service = Get-Service -Name $serviceName -ErrorAction SilentlyContinue
    } while (-not $service -and $waitTime -lt $maxWaitTime)
    
    if (-not $service) {
        throw "SCOM Agent service ($serviceName) not found after installation"
    }
    
    if ($service.Status -ne "Running") {
        Write-Log "Starting SCOM Agent service..."
        Start-Service -Name $serviceName
        
        $service.WaitForStatus("Running", [TimeSpan]::FromSeconds(60))
        Write-Log "SCOM Agent service started successfully"
    }
    
    try {
        $agentConfigPath = Join-Path $InstallPath "Tools\AgentConfigManager.exe"
        if (Test-Path $agentConfigPath) {
            Write-Log "Configuring management group using AgentConfigManager..."
            
            $configArgs = @(
                "/Action:Add",
                "/ManagementServerAddress:$ManagementServer",
                "/ManagementGroupName:$ResolvedManagementGroup"
            )
            
            $configProcess = Start-Process -FilePath $agentConfigPath -ArgumentList $configArgs -Wait -PassThru -NoNewWindow
            
            if ($configProcess.ExitCode -eq 0) {
                Write-Log "Management group configuration completed successfully"
            }
            else {
                Write-Log "Management group configuration completed with exit code: $($configProcess.ExitCode)" -Level "WARNING"
            }
        }
    }
    catch {
        Write-Log "Warning: Could not configure management group using AgentConfigManager: $($_.Exception.Message)" -Level "WARNING"
    }
}

function Test-SCOMAgentInstallation {
    param([string]$ResolvedManagementGroup)
    
    Write-Log "Validating SCOM Agent installation..."
    
    $installedAgent = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Microsoft Monitoring Agent*" }
    if (-not $installedAgent) {
        throw "SCOM Agent installation validation failed - Agent not found in installed programs"
    }
    
    Write-Log "Installed Agent: $($installedAgent.Name) - Version: $($installedAgent.Version)"
    
    $service = Get-Service -Name $config.SCOMConfig.SCOMServiceName -ErrorAction SilentlyContinue
    if (-not $service) {
        throw "SCOM Agent service ($($config.SCOMConfig.SCOMServiceName)) not found"
    }
    
    if ($service.Status -ne "Running") {
        throw "SCOM Agent service is not running. Status: $($service.Status)"
    }
    
    Write-Log "SCOM Agent service is running successfully"
    
    $registryPath = "HKLM:\SOFTWARE\Microsoft\Microsoft Operations Manager\3.0\Agent Management Groups\$ResolvedManagementGroup"
    if (Test-Path $registryPath) {
        $mgServer = Get-ItemProperty -Path $registryPath -Name "NetworkName" -ErrorAction SilentlyContinue
        if ($mgServer) {
            Write-Log "Management Group configured: $ResolvedManagementGroup"
            Write-Log "Management Server: $($mgServer.NetworkName)"
        }
    }
    else {
        Write-Log "Warning: Management group registry entry not found. Agent may need manual configuration." -Level "WARNING"
    }
    
    try {
        $recentEvents = Get-WinEvent -LogName "Operations Manager" -MaxEvents 5 -ErrorAction SilentlyContinue | 
        Where-Object { $_.TimeCreated -gt (Get-Date).AddMinutes(-30) }
        
        if ($recentEvents) {
            Write-Log "Recent Operations Manager events found - Agent appears to be communicating"
        }
    }
    catch {
        Write-Log "Could not check Operations Manager event log" -Level "WARNING"
    }
    
    Write-Log "SCOM Agent installation validation completed successfully"
}

function ConfigSCOM {
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]
        [string]$serverName,
        
        [Parameter(Mandatory=$true)]
        [string]$ManagementServer,
        
        [Parameter(Mandatory=$true)]
        [string]$ManagementGroup,
        
        [string]$InstallPath,
        
        [string]$ActionAccount,
        
        [SecureString]$ActionAccountPassword,
        
        [switch]$Force,
        
        [ValidateSet("Shared", "MSSQL")]
        [string]$AppType = "Shared",
        
        [int]$ManagementPort,
        
        [string]$SourceServer = $defaultSourceServer,
        
        [string]$SourcePath = $defaultSourcePath,
        
        [Diagnostics.CodeAnalysis.SuppressMessageAttribute('PSAvoidUsingPlainTextForPassword', 'CredentialTarget', Justification = 'CredentialTarget is not a password - it specifies which credential file to use')]
        [ValidateSet("ppe", "dev", "admin", "cli", "")]
        [string]$CredentialTarget = ""
    )
    
    # Set default values from config if not provided
    if (-not $InstallPath) {
        $InstallPath = $config.SCOMConfig.SCOMDefaultInstallPath
    }

    if (-not $ManagementPort) {
        $ManagementPort = $config.SCOMConfig.SCOMManagementPort
    }
    
    try {
        Write-Log "Starting remote SCOM agent installation on server: $serverName"
        Write-Log "Source: \\$SourceServer\$($SourcePath.Replace(':', '$'))"
        
        Write-Log "Loading stored credentials for remote installation..."
        
        try {
            $credFile = $mudCreds
            
            if ($CredentialTarget) {
                switch ($CredentialTarget.ToLower()) {
                    "ppe" { $credFile = $ppeCreds }
                    "dev" { $credFile = $devCreds }
                    "admin" { $credFile = $adminCredLocation }
                    "cli" { $credFile = $cliCredsPath }
                    default { $credFile = $mudCreds }
                }
            }
            
            Write-Log "Using credential file: $credFile"
            
            if (-not (Test-Path $credFile)) {
                throw "Credential file not found: $credFile. This file is required for remote installation."
            }
            
            $user = Import-Clixml -Path $credFile
            Write-Log "Successfully loaded credentials for user: $($user.UserName)"
            
        } catch {
            Write-Log "Failed to load credentials: $($_.Exception.Message)" -Level "ERROR"
            throw "Remote installation cannot proceed without valid credentials. Error: $($_.Exception.Message)"
        }
        
        $actualManagementGroup = Get-SCOMManagementGroup -BaseManagementGroup $ManagementGroup -ApplicationType $AppType
        Write-Log "Resolved Management Group: $actualManagementGroup"
        
        try {
            $installResult = Invoke-Command -ComputerName $serverName -Credential $user -ScriptBlock {    
                param (
                    $sourceServer,
                    $sourcePath,
                    $managementServer,
                    $managementGroup,
                    $managementPort,
                    $installPath,
                    $actionAccount,
                    $actionAccountPassword,
                    $force
                )
                
                $existingAgent = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Microsoft Monitoring Agent*" }
                if ($existingAgent -and -not $force) {
                    return @{
                        Success = $false
                        Message = "SCOM Agent is already installed. Use -Force parameter to reinstall."
                        ExitCode = 1619
                    }
                }
                
                $osVersion = (Get-WmiObject -Class Win32_OperatingSystem).Caption
                if ($osVersion -match "2019") {
                    try {
                        $registryKeyPath = "HKLM:\SYSTEM\CurrentControlSet\Services\EventLog\Security"
                        if (Test-Path -Path $registryKeyPath) {
                            Remove-ItemProperty -Path $registryKeyPath -Name CustomSD -Force -ErrorAction SilentlyContinue
                            Write-Output "Removed CustomSD registry property for Windows 2019 compatibility"
                        }
                    } catch {
                        Write-Output "Warning: Could not remove CustomSD registry property: $($_.Exception.Message)"
                    }
                }
                
                $uncPath = "\\$sourceServer\$($sourcePath.Replace(':', '$'))"
                
                $installArgs = @(
                    "/i", "`"$uncPath`"",
                    "/qn",
                    "USE_SETTINGS_FROM_AD=0",
                    "USE_MANUALLY_SPECIFIED_SETTINGS=1",
                    "MANAGEMENT_GROUP=`"$managementGroup`"",
                    "MANAGEMENT_SERVER_DNS=`"$managementServer`"",
                    "SECURE_PORT=`"$managementPort`"",
                    "AcceptEndUserLicenseAgreement=1",
                    "/l*v", "`"$($config.SCOMConfig.SCOMDefaultLogPath)`""
                )
                
                if ($actionAccount -and $actionAccountPassword) {
                    $installArgs += "ACTIONS_USE_COMPUTER_ACCOUNT=0"
                    $installArgs += "ACTIONSDOMAIN=`"$($actionAccount.Split('\')[0])`""
                    $installArgs += "ACTIONSUSER=`"$($actionAccount.Split('\')[1])`""
                    
                    if ($actionAccountPassword -is [SecureString]) {
                        $plainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($actionAccountPassword))
                    } else {
                        $plainPassword = $actionAccountPassword
                    }
                    $installArgs += "ACTIONSPASSWORD=`"$plainPassword`""
                } else {
                    $installArgs += "ACTIONS_USE_COMPUTER_ACCOUNT=1"
                }
                
                if ($installPath -ne $config.SCOMConfig.SCOMDefaultInstallPath) {
                    $installArgs += "INSTALLDIR=`"$installPath`""
                }
                
                try {
                    $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $installArgs -Wait -PassThru -NoNewWindow
                    
                    if ($process.ExitCode -eq 0) {
                        return @{
                            Success = $true
                            Message = "SCOM Agent installation completed successfully"
                            ExitCode = $process.ExitCode
                            OSVersion = $osVersion
                        }
                    }
                    elseif ($process.ExitCode -eq 3010) {
                        return @{
                            Success = $true
                            Message = "SCOM Agent installation completed successfully. Reboot required."
                            ExitCode = $process.ExitCode
                            OSVersion = $osVersion
                        }
                    }
                    else {
                        return @{
                            Success = $false
                            Message = "SCOM Agent installation failed with exit code: $($process.ExitCode)"
                            ExitCode = $process.ExitCode
                            OSVersion = $osVersion
                        }
                    }
                }
                catch {
                    return @{
                        Success = $false
                        Message = "Failed to install SCOM Agent: $($_.Exception.Message)"
                        ExitCode = -1
                        OSVersion = $osVersion
                    }
                }
            } -ArgumentList $SourceServer, $SourcePath, $ManagementServer, $actualManagementGroup, $ManagementPort, $InstallPath, $ActionAccount, $ActionAccountPassword, $Force.IsPresent
            
            Write-Log "Installation result: $($installResult.Message)"
            if ($installResult.OSVersion) {
                Write-Log "Target server OS: $($installResult.OSVersion)"
            }
            
            if ($installResult.Success) {
                Start-Sleep 60
                
                $verificationResult = Invoke-Command -ComputerName $serverName -Credential $user -ScriptBlock {
                    $service = Get-Service -Name $config.SCOMConfig.SCOMServiceName -ErrorAction SilentlyContinue
                    $installedAgent = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Microsoft Monitoring Agent*" }
                    
                    return @{
                        ServiceStatus = if ($service) { $service.Status.ToString() } else { "NotFound" }
                        AgentInstalled = if ($installedAgent) { $true } else { $false }
                        AgentVersion = if ($installedAgent) { $installedAgent.Version } else { "N/A" }
                    }
                }
                
                if ($verificationResult.AgentInstalled) {
                    Write-Log "SCOM Agent verification successful - Agent installed and service status: $($verificationResult.ServiceStatus)"
                } else {
                    Write-Log "SCOM Agent verification failed - Agent not found" -Level "ERROR"
                }
            } else {
                Write-Log "SCOM Agent installation failed: $($installResult.Message)" -Level "ERROR"
                throw "Remote installation failed: $($installResult.Message)"
            }
            
        }
        catch {
            Write-Log "Remote SCOM installation failed: $($_.Exception.Message)" -Level "ERROR"
            throw $_
        }
    }
}

function Install-SCOMAgentRemote {
    <#
    .SYNOPSIS
        Simplified wrapper function for API-driven remote SCOM agent installation.
    
    .DESCRIPTION
        This function provides a simplified interface for API calls to install SCOM agents
        on remote servers. It automatically handles credential loading from XML files
        without requiring interactive input.
    
    .PARAMETER ServerName
        The name of the remote server where the SCOM agent will be installed.
    
    .PARAMETER ManagementServer
        The primary SCOM management server FQDN or IP address.
    
    .PARAMETER ManagementGroup
        The name of the SCOM management group.
    
    .PARAMETER InstallPath
        Optional installation path for the SCOM agent.
    
    .PARAMETER ActionAccount
        Optional action account to use for the agent.
    
    .PARAMETER ActionAccountPassword
        Optional password for the action account.
    
    .PARAMETER AppType
        The application type ('Shared' or 'MSSQL') to determine management group.
    
    .PARAMETER ManagementPort
        Optional SCOM management server port number.
    
    .PARAMETER CredentialTarget
        Optional credential target to specify which XML credential file to use.
        Valid values: 'ppe', 'dev', 'admin', 'cli'. Defaults to production credentials.
    
    .PARAMETER Force
        Force reinstallation even if agent is already installed.
    
    .EXAMPLE
        Install-SCOMAgentRemote -ServerName "Server01" -ManagementServer "SRV009365.mud.internal.co.za" -ManagementGroup "ITISSOPSMGR_2019"
    
    .EXAMPLE
        Install-SCOMAgentRemote -ServerName "Server01" -ManagementServer "SRV009365.mud.internal.co.za" -ManagementGroup "ITISSOPSMGR_2019" -CredentialTarget "admin" -AppType "MSSQL" -Force
    #>
    [CmdletBinding()]
    param (
        [Parameter(Mandatory=$true)]
        [string]$ServerName,
        
        [Parameter(Mandatory=$true)]
        [string]$ManagementServer,
        
        [Parameter(Mandatory=$true)]
        [string]$ManagementGroup,
        
        [string]$InstallPath,
        
        [string]$ActionAccount,
        
        [SecureString]$ActionAccountPassword,
        
        [ValidateSet("Shared", "MSSQL")]
        [string]$AppType = "Shared",
        
        [int]$ManagementPort,
        
        [ValidateSet("ppe", "dev", "admin", "cli", "")]
        [string]$CredentialTarget = "",
        
        [switch]$Force
    )
    
    # Set default values from config if not provided
    if (-not $InstallPath) {
        $InstallPath = $config.SCOMConfig.SCOMDefaultInstallPath
    }

    if (-not $ManagementPort) {
        $ManagementPort = $config.SCOMConfig.SCOMManagementPort
    }
    
    try {
        Write-Log "Starting remote SCOM agent installation on server: $serverName"
        Write-Log "Source: \\$SourceServer\$($SourcePath.Replace(':', '$'))"
        
        Write-Log "Loading stored credentials for remote installation..."
        
        try {
            $credFile = $mudCreds
            
            if ($CredentialTarget) {
                switch ($CredentialTarget.ToLower()) {
                    "ppe" { $credFile = $ppeCreds }
                    "dev" { $credFile = $devCreds }
                    "admin" { $credFile = $adminCredLocation }
                    "cli" { $credFile = $cliCredsPath }
                    default { $credFile = $mudCreds }
                }
            }
            
            Write-Log "Using credential file: $credFile"
            
            if (-not (Test-Path $credFile)) {
                throw "Credential file not found: $credFile. This file is required for remote installation."
            }
            
            $user = Import-Clixml -Path $credFile
            Write-Log "Successfully loaded credentials for user: $($user.UserName)"
            
        } catch {
            Write-Log "Failed to load credentials: $($_.Exception.Message)" -Level "ERROR"
            throw "Remote installation cannot proceed without valid credentials. Error: $($_.Exception.Message)"
        }
        
        $actualManagementGroup = Get-SCOMManagementGroup -BaseManagementGroup $ManagementGroup -ApplicationType $AppType
        Write-Log "Resolved Management Group: $actualManagementGroup"
        
        try {
            $installResult = Invoke-Command -ComputerName $serverName -Credential $user -ScriptBlock {    
                param (
                    $sourceServer,
                    $sourcePath,
                    $managementServer,
                    $managementGroup,
                    $managementPort,
                    $installPath,
                    $actionAccount,
                    $actionAccountPassword,
                    $force
                )
                
                $existingAgent = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Microsoft Monitoring Agent*" }
                if ($existingAgent -and -not $force) {
                    return @{
                        Success = $false
                        Message = "SCOM Agent is already installed. Use -Force parameter to reinstall."
                        ExitCode = 1619
                    }
                }
                
                $osVersion = (Get-WmiObject -Class Win32_OperatingSystem).Caption
                if ($osVersion -match "2019") {
                    try {
                        $registryKeyPath = "HKLM:\SYSTEM\CurrentControlSet\Services\EventLog\Security"
                        if (Test-Path -Path $registryKeyPath) {
                            Remove-ItemProperty -Path $registryKeyPath -Name CustomSD -Force -ErrorAction SilentlyContinue
                            Write-Output "Removed CustomSD registry property for Windows 2019 compatibility"
                        }
                    } catch {
                        Write-Output "Warning: Could not remove CustomSD registry property: $($_.Exception.Message)"
                    }
                }
                
                $uncPath = "\\$sourceServer\$($sourcePath.Replace(':', '$'))"
                
                $installArgs = @(
                    "/i", "`"$uncPath`"",
                    "/qn",
                    "USE_SETTINGS_FROM_AD=0",
                    "USE_MANUALLY_SPECIFIED_SETTINGS=1",
                    "MANAGEMENT_GROUP=`"$managementGroup`"",
                    "MANAGEMENT_SERVER_DNS=`"$managementServer`"",
                    "SECURE_PORT=`"$managementPort`"",
                    "AcceptEndUserLicenseAgreement=1",
                    "/l*v", "`"$($config.SCOMConfig.SCOMDefaultLogPath)`""
                )
                
                if ($actionAccount -and $actionAccountPassword) {
                    $installArgs += "ACTIONS_USE_COMPUTER_ACCOUNT=0"
                    $installArgs += "ACTIONSDOMAIN=`"$($actionAccount.Split('\')[0])`""
                    $installArgs += "ACTIONSUSER=`"$($actionAccount.Split('\')[1])`""
                    
                    if ($actionAccountPassword -is [SecureString]) {
                        $plainPassword = [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($actionAccountPassword))
                    } else {
                        $plainPassword = $actionAccountPassword
                    }
                    $installArgs += "ACTIONSPASSWORD=`"$plainPassword`""
                } else {
                    $installArgs += "ACTIONS_USE_COMPUTER_ACCOUNT=1"
                }
                
                if ($installPath -ne $config.SCOMConfig.SCOMDefaultInstallPath) {
                    $installArgs += "INSTALLDIR=`"$installPath`""
                }
                
                try {
                    $process = Start-Process -FilePath "msiexec.exe" -ArgumentList $installArgs -Wait -PassThru -NoNewWindow
                    
                    if ($process.ExitCode -eq 0) {
                        return @{
                            Success = $true
                            Message = "SCOM Agent installation completed successfully"
                            ExitCode = $process.ExitCode
                            OSVersion = $osVersion
                        }
                    }
                    elseif ($process.ExitCode -eq 3010) {
                        return @{
                            Success = $true
                            Message = "SCOM Agent installation completed successfully. Reboot required."
                            ExitCode = $process.ExitCode
                            OSVersion = $osVersion
                        }
                    }
                    else {
                        return @{
                            Success = $false
                            Message = "SCOM Agent installation failed with exit code: $($process.ExitCode)"
                            ExitCode = $process.ExitCode
                            OSVersion = $osVersion
                        }
                    }
                }
                catch {
                    return @{
                        Success = $false
                        Message = "Failed to install SCOM Agent: $($_.Exception.Message)"
                        ExitCode = -1
                        OSVersion = $osVersion
                    }
                }
            } -ArgumentList $SourceServer, $SourcePath, $ManagementServer, $actualManagementGroup, $ManagementPort, $InstallPath, $ActionAccount, $ActionAccountPassword, $Force.IsPresent
            
            Write-Log "Installation result: $($installResult.Message)"
            if ($installResult.OSVersion) {
                Write-Log "Target server OS: $($installResult.OSVersion)"
            }
            
            if ($installResult.Success) {
                Start-Sleep 60
                
                $verificationResult = Invoke-Command -ComputerName $serverName -Credential $user -ScriptBlock {
                    $service = Get-Service -Name $config.SCOMConfig.SCOMServiceName -ErrorAction SilentlyContinue
                    $installedAgent = Get-WmiObject -Class Win32_Product | Where-Object { $_.Name -like "*Microsoft Monitoring Agent*" }
                    
                    return @{
                        ServiceStatus = if ($service) { $service.Status.ToString() } else { "NotFound" }
                        AgentInstalled = if ($installedAgent) { $true } else { $false }
                        AgentVersion = if ($installedAgent) { $installedAgent.Version } else { "N/A" }
                    }
                }
                
                if ($verificationResult.AgentInstalled) {
                    Write-Log "SCOM Agent verification successful - Agent installed and service status: $($verificationResult.ServiceStatus)"
                } else {
                    Write-Log "SCOM Agent verification failed - Agent not found" -Level "ERROR"
                }
            } else {
                Write-Log "SCOM Agent installation failed: $($installResult.Message)" -Level "ERROR"
                throw "Remote installation failed: $($installResult.Message)"
            }
            
        }
        catch {
            Write-Log "Remote SCOM installation failed: $($_.Exception.Message)" -Level "ERROR"
            throw $_
        }
    }
}

### Main Execution ###

try {
    Write-Log "=== SCOM Agent Installation Started ==="
    
    if ($ServerName) {
        Write-Log "Remote installation requested for server: $ServerName"
        
        $params = @{
            serverName = $ServerName
            ManagementServer = $ManagementServer
            ManagementGroup = $ManagementGroup
            InstallPath = $InstallPath
            AppType = $AppType
            ManagementPort = $ManagementPort
        }
        
        if ($ActionAccount) {
            $params.ActionAccount = $ActionAccount
        }
        
        if ($ActionAccountPassword) {
            $params.ActionAccountPassword = $ActionAccountPassword
        }
        
        if ($CredentialTarget) {
            $params.CredentialTarget = $CredentialTarget
        }
        
        if ($Force) { $params.Force = $true }
        
        ConfigSCOM @params
        
        $actualManagementGroup = Get-SCOMManagementGroup -BaseManagementGroup $ManagementGroup -ApplicationType $AppType
        $message = "SCOM agent installation completed successfully on remote server: $ServerName (Management Group: $actualManagementGroup)"
        Write-Log $message
        
        if ($OutputJson) {
            $result = New-JsonReturn -Success "true" -Status "REMOTE_INSTALLED" -Message $message -Data @{
                ServerName = $ServerName
                InstallationType = "Remote"
                ResolvedManagementGroup = $actualManagementGroup
            }
            Write-Output $result
        } else {
            Write-Host $message -ForegroundColor Green
        }
        
        exit 0
    }
    
    Write-Log "Local installation on: $env:COMPUTERNAME"
    Write-Log "Management Server: $ManagementServer"
    Write-Log "Management Port: $ManagementPort"
    Write-Log "Base Management Group: $ManagementGroup"
    Write-Log "Application Type: $AppType"
    Write-Log "Installation Path: $InstallPath"
    Write-Log "Log Path: $LogPath"
    
    $actualManagementGroup = Get-SCOMManagementGroup -BaseManagementGroup $ManagementGroup -ApplicationType $AppType
    Write-Log "Resolved Management Group: $actualManagementGroup"
    
    $script:actualManagementGroup = $actualManagementGroup
    
    Test-Prerequisites
    
    $installerPath = Get-SCOMAgentInstaller
    
    Install-SCOMAgent -InstallerPath $installerPath -ResolvedManagementGroup $actualManagementGroup
    
    Set-SCOMAgentConfiguration -ResolvedManagementGroup $actualManagementGroup
    
    Test-SCOMAgentInstallation -ResolvedManagementGroup $actualManagementGroup
    
    Write-Log "=== SCOM Agent Installation Completed Successfully ==="
    Write-Log "The server may require a reboot to complete the installation."
    
    if ($OutputJson) {
        $successResponse = New-JsonReturn -Success "true" -Status "completed" -Message "SCOM Agent installation completed successfully"
        Write-Output $successResponse
    }
    
}
catch {
    Write-Log "=== SCOM Agent Installation Failed ===" -Level "ERROR"
    Write-Log "Error: $($_.Exception.Message)" -Level "ERROR"
    Write-Log "Check the installation log at: $LogPath" -Level "ERROR"
    
    if ($OutputJson) {
        $errorData = @{
            errorDetails = $_.Exception.Message
            logPath      = $LogPath
        }
        $errorResponse = New-JsonReturn -Success "false" -Status "failed" -Message "SCOM Agent installation failed" -Data $errorData
        Write-Output $errorResponse
    }
    
    exit 1
}
