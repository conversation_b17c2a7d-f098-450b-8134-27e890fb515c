{"GlobalConfig": {"AppSourcePath": "\\\\srv009484\\AutoDeploy$", "CredPath": "C:\\Scripts\\Credentials", "ScriptPath": "C:\\Scripts", "ReportPath": "C:\\Scripts\\Reports", "ModulesPath": "C:\\Scripts\\Modules", "LogsPath": "C:\\Scripts\\Logs"}, "SCCMConfig": {"SCCMSourceServer": "srv009484", "SCCMClientPath": "D:\\AutoDeploy\\ServerInstalls\\SCCM\\client\\ccmsetup.exe", "SCCMManagementPort": 80}, "SCOMConfig": {"SCOMSourceServer": "srv009484", "SCOMAgentPath": "D:\\AutoDeploy\\ServerInstalls\\SCOM\\agent\\AMD64\\MOMAgent.msi", "SCOMManagementPort": 5723, "SCOMDefaultInstallPath": "C:\\Program Files\\Microsoft Monitoring Agent\\Agent", "SCOMDefaultLogPath": "C:\\Windows\\Temp\\SCOMAgent_Install.log", "SCOMTempPath": "C:\\Temp\\ServerInstalls\\SCOM", "SCOMAgentInstaller": "MOMAgent.msi", "SCOMDefaultManagementServer": "SRV009365.mud.internal.co.za", "SCOMDefaultManagementGroup": "ITISSOPSMGR_2019", "SCOMSQLManagementGroup": "SGT - SQL Server Management", "SCOMSupportedOSVersions": ["Server 2016", "Server 2019", "Server 2022"], "SCOMServiceName": "HealthService"}, "QualysConfig": {"CustomerId": "41777429-87a7-f49e-8287-1d2bbd165875", "WebServiceUri": "https://qagpublic.qg2.apps.qualys.eu/CloudAgent/", "InstallPath": "C:\\Program Files\\Qualys\\QualysAgent", "LogPath": "C:\\Windows\\Temp\\QualysAgent_Install.log", "TempPath": "C:\\Windows\\Temp", "AgentInstaller": "QualysCloudAgent.exe", "AgentSourcePath": "\\\\srv010117\\d$\\New Server Deployments\\New Server Build\\QualysAgents\\[Datacenter]\\QualysCloudAgent.exe", "CertificateSourcePath": "\\\\srv010117\\d$\\New Server Deployments\\New Server Build\\QualysAgents\\Certificate\\WIN.msi", "PemFilePath": "\\\\srv010117\\d$\\New Server Deployments\\New Server Build\\QualysAgents\\Certificate\\Appliance-Certificate.pem", "SourceServer": "srv010117", "SourcePath": "d$\\New Server Deployments\\New Server Build\\QualysAgents", "DatacenterConfig": {"BDC": {"ActivationId": "ba4039bc-540e-43fd-817f-1cb8bc9f3d57", "ProxyServers": ["http://SDV041444.mud.internal.co.za:8080", "http://SDV041444.mud.internal.co.za:1080", "http://SDV041445.mud.internal.co.za:8080", "http://SDV041445.mud.internal.co.za:1080", "http://SDV041446.mud.internal.co.za:8080", "http://SDV041446.mud.internal.co.za:1080", "http://SDV041447.mud.internal.co.za:8080", "http://SDV041447.mud.internal.co.za:1080"]}, "CDC": {"ActivationId": "29ce782e-eacd-4a6a-b181-baac856da6ff", "ProxyServers": ["http://SDV041445.mud.internal.co.za:8080", "http://SDV041445.mud.internal.co.za:1080", "http://SDV041447.mud.internal.co.za:8080", "http://SDV041447.mud.internal.co.za:1080"]}}}}